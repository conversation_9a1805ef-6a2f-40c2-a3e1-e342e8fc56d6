'use client'

import React, { useState, useEffect } from 'react'
import { motion, useAnimation } from 'framer-motion'

import { useTypingAnimation } from '../../hooks/useTypingAnimation'
import { useMediaQuery } from '@/hooks/useMediaQuery'
import HeroB<PERSON>ground from './HeroBackground'
import HeroT<PERSON><PERSON> from './HeroTitle'
import TypingSubtitle from './TypingSubtitle'
import HeroCTA from './HeroCTA'

// Constants
const DESKTOP_PHRASES = [
  'Formación In Company',
  'Asesoría Estratégica',
  'Desarrollo de Cursos',
  'Automatizaciones',
  'Desarrollo a Medida',
]

const MOBILE_PHRASES = [
  'Formación',
  'Asesoría',
  'Cursos',
  'Automatización',
  'Desarrollo',
]

/**
 * Refactored HeroSection component following ATLAS v2.4 principles
 * 
 * IMPROVEMENTS:
 * - Reduced from 269 lines to <100 lines
 * - Extracted reusable hooks (useTypingAnimation)
 * - Modular component architecture
 * - Better separation of concerns
 * - Improved testability
 * - Consistent animation patterns
 * 
 * @returns JSX.Element
 */
const HeroSectionRefactored: React.FC = () => {
  // Animation controls
  const controls = useAnimation()
  
  // Responsive detection
  const isMobile = useMediaQuery('(max-width: 640px)')
  
  // State
  const [isMounted, setIsMounted] = useState(false)
  
  // Typing animation with responsive phrases
  const phrases = isMobile ? MOBILE_PHRASES : DESKTOP_PHRASES
  const typingAnimation = useTypingAnimation({
    phrases,
    typingSpeed: 150,
    deletingSpeed: 80,
    pauseDuration: 1500,
    loop: true,
  })

  // Initialize component
  useEffect(() => {
    setIsMounted(true)
    controls.start('visible')
  }, [controls])

  // Container animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  }

  // Don't render until mounted to avoid hydration issues
  if (!isMounted) {
    return (
      <HeroBackground>
        <div className="min-h-[60vh] flex items-center justify-center">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-[#00F0FF] border-t-transparent rounded-full animate-spin mx-auto" />
          </div>
        </div>
      </HeroBackground>
    )
  }

  return (
    <HeroBackground>
      <motion.div
        className="max-w-6xl mx-auto text-center"
        variants={containerVariants}
        initial="hidden"
        animate={controls}
      >
        {/* Main Title */}
        <HeroTitle />

        {/* Typing Subtitle */}
        <TypingSubtitle
          text={typingAnimation.currentText}
          isTyping={typingAnimation.isTyping}
          className="mb-8 sm:mb-12"
        />

        {/* Call to Action Buttons */}
        <HeroCTA />
      </motion.div>
    </HeroBackground>
  )
}

export default HeroSectionRefactored
