'use client'

import React, { useEffect, useCallback, useRef } from 'react'
import { createPortal } from 'react-dom'
import { motion, AnimatePresence } from 'framer-motion'
import { X } from 'lucide-react'
import { cn } from '@/utils/cn'
import { useThemeStyles } from '../../hooks/useThemeStyles'

// Types
export interface ModalProps {
  // Visibility
  isOpen: boolean
  onClose: () => void
  
  // Content
  children: React.ReactNode
  title?: string
  description?: string
  
  // Appearance
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  variant?: 'default' | 'centered' | 'drawer' | 'fullscreen'
  
  // Behavior
  closeOnOverlayClick?: boolean
  closeOnEscape?: boolean
  showCloseButton?: boolean
  preventScroll?: boolean
  
  // Animation
  animate?: boolean
  
  // Styling
  className?: string
  overlayClassName?: string
  contentClassName?: string
  
  // Accessibility
  'aria-labelledby'?: string
  'aria-describedby'?: string
  role?: string
  
  // Events
  onOverlayClick?: () => void
  onEscapePress?: () => void
}

/**
 * ATLAS v2.4 Modal Component
 * 
 * Comprehensive modal component with multiple variants and full accessibility
 * 
 * Features:
 * - 4 size options (sm, md, lg, xl, full)
 * - 4 variants (default, centered, drawer, fullscreen)
 * - Overlay and escape key handling
 * - Focus management and accessibility
 * - Smooth animations
 * - Portal rendering
 * - Scroll prevention
 * - Theme-aware styling
 * 
 * @param props - Modal properties
 * @returns Modal component
 */
const Modal: React.FC<ModalProps> = ({
  // Visibility
  isOpen,
  onClose,
  
  // Content
  children,
  title,
  description,
  
  // Appearance
  size = 'md',
  variant = 'default',
  
  // Behavior
  closeOnOverlayClick = true,
  closeOnEscape = true,
  showCloseButton = true,
  preventScroll = true,
  
  // Animation
  animate = true,
  
  // Styling
  className = '',
  overlayClassName = '',
  contentClassName = '',
  
  // Accessibility
  'aria-labelledby': ariaLabelledBy,
  'aria-describedby': ariaDescribedBy,
  role = 'dialog',
  
  // Events
  onOverlayClick,
  onEscapePress,
}) => {
  const themeStyles = useThemeStyles()
  const modalRef = useRef<HTMLDivElement>(null)
  const previousActiveElement = useRef<HTMLElement | null>(null)
  
  // Size styles
  const sizeStyles = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    full: 'max-w-full mx-4',
  }
  
  // Variant styles
  const variantStyles = {
    default: 'fixed inset-0 z-50 flex items-center justify-center p-4',
    centered: 'fixed inset-0 z-50 flex items-center justify-center p-4',
    drawer: 'fixed inset-y-0 right-0 z-50 flex',
    fullscreen: 'fixed inset-0 z-50 flex',
  }
  
  // Content styles based on variant
  const contentVariantStyles = {
    default: `${themeStyles.background.secondary} rounded-lg shadow-xl`,
    centered: `${themeStyles.background.secondary} rounded-lg shadow-xl`,
    drawer: `${themeStyles.background.secondary} w-full max-w-sm shadow-xl`,
    fullscreen: `${themeStyles.background.secondary} w-full h-full`,
  }
  
  // Handle escape key
  const handleEscapeKey = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape' && closeOnEscape) {
      onEscapePress?.()
      onClose()
    }
  }, [closeOnEscape, onClose, onEscapePress])
  
  // Handle overlay click
  const handleOverlayClick = useCallback((event: React.MouseEvent) => {
    if (event.target === event.currentTarget && closeOnOverlayClick) {
      onOverlayClick?.()
      onClose()
    }
  }, [closeOnOverlayClick, onClose, onOverlayClick])
  
  // Focus management
  useEffect(() => {
    if (isOpen) {
      // Store the currently focused element
      previousActiveElement.current = document.activeElement as HTMLElement
      
      // Focus the modal
      setTimeout(() => {
        modalRef.current?.focus()
      }, 100)
      
      // Add escape key listener
      document.addEventListener('keydown', handleEscapeKey)
      
      // Prevent scroll if enabled
      if (preventScroll) {
        document.body.style.overflow = 'hidden'
      }
    } else {
      // Restore focus to the previously focused element
      if (previousActiveElement.current) {
        previousActiveElement.current.focus()
      }
      
      // Remove escape key listener
      document.removeEventListener('keydown', handleEscapeKey)
      
      // Restore scroll
      if (preventScroll) {
        document.body.style.overflow = 'unset'
      }
    }
    
    return () => {
      document.removeEventListener('keydown', handleEscapeKey)
      if (preventScroll) {
        document.body.style.overflow = 'unset'
      }
    }
  }, [isOpen, handleEscapeKey, preventScroll])
  
  // Animation variants
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { duration: 0.2, ease: 'easeOut' }
    },
    exit: { 
      opacity: 0,
      transition: { duration: 0.2, ease: 'easeIn' }
    },
  }
  
  const contentVariants = {
    default: {
      hidden: { opacity: 0, scale: 0.95, y: 20 },
      visible: { 
        opacity: 1, 
        scale: 1, 
        y: 0,
        transition: { duration: 0.3, ease: 'easeOut' }
      },
      exit: { 
        opacity: 0, 
        scale: 0.95, 
        y: 20,
        transition: { duration: 0.2, ease: 'easeIn' }
      },
    },
    drawer: {
      hidden: { x: '100%' },
      visible: { 
        x: 0,
        transition: { duration: 0.3, ease: 'easeOut' }
      },
      exit: { 
        x: '100%',
        transition: { duration: 0.2, ease: 'easeIn' }
      },
    },
    fullscreen: {
      hidden: { opacity: 0 },
      visible: { 
        opacity: 1,
        transition: { duration: 0.2, ease: 'easeOut' }
      },
      exit: { 
        opacity: 0,
        transition: { duration: 0.2, ease: 'easeIn' }
      },
    },
  }
  
  // Don't render if not open and no animation
  if (!isOpen && !animate) return null
  
  // Portal target
  const portalTarget = typeof window !== 'undefined' ? document.body : null
  
  if (!portalTarget) return null
  
  const modalContent = (
    <AnimatePresence mode="wait">
      {isOpen && (
        <div className={cn(variantStyles[variant], className)}>
          {/* Overlay */}
          <motion.div
            className={cn(
              'absolute inset-0 bg-black/50 backdrop-blur-sm',
              overlayClassName
            )}
            variants={animate ? overlayVariants : undefined}
            initial={animate ? 'hidden' : undefined}
            animate={animate ? 'visible' : undefined}
            exit={animate ? 'exit' : undefined}
            onClick={handleOverlayClick}
          />
          
          {/* Content */}
          <motion.div
            ref={modalRef}
            className={cn(
              'relative w-full',
              sizeStyles[size],
              contentVariantStyles[variant],
              contentClassName
            )}
            variants={animate ? contentVariants[variant === 'drawer' ? 'drawer' : variant === 'fullscreen' ? 'fullscreen' : 'default'] : undefined}
            initial={animate ? 'hidden' : undefined}
            animate={animate ? 'visible' : undefined}
            exit={animate ? 'exit' : undefined}
            role={role}
            aria-modal="true"
            aria-labelledby={ariaLabelledBy || (title ? 'modal-title' : undefined)}
            aria-describedby={ariaDescribedBy || (description ? 'modal-description' : undefined)}
            tabIndex={-1}
          >
            {/* Header */}
            {(title || showCloseButton) && (
              <div className="flex items-center justify-between p-6 border-b border-current/10">
                <div>
                  {title && (
                    <h2 
                      id="modal-title"
                      className={cn(
                        'text-lg font-semibold',
                        themeStyles.text.primary
                      )}
                    >
                      {title}
                    </h2>
                  )}
                  {description && (
                    <p 
                      id="modal-description"
                      className={cn(
                        'mt-1 text-sm',
                        themeStyles.text.secondary
                      )}
                    >
                      {description}
                    </p>
                  )}
                </div>
                
                {showCloseButton && (
                  <button
                    onClick={onClose}
                    className={cn(
                      'p-2 rounded-lg transition-colors',
                      themeStyles.interactive.hover,
                      themeStyles.text.secondary
                    )}
                    aria-label="Close modal"
                  >
                    <X className="w-5 h-5" />
                  </button>
                )}
              </div>
            )}
            
            {/* Body */}
            <div className="p-6">
              {children}
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  )
  
  return createPortal(modalContent, portalTarget)
}

Modal.displayName = 'Modal'

export default Modal
