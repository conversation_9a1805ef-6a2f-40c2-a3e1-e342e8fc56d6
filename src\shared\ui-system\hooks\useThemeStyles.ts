/**
 * ATLAS v2.4 - Unified Theme Styles Hook
 * Centralizes theme-aware styling logic to eliminate duplication
 */

import { useMemo } from 'react'
import { useTheme } from '@/context/ThemeContext'
import { useDesignTokens } from './useDesignTokens'

/**
 * Common theme-aware style patterns
 */
interface ThemeStylePatterns {
  // Background patterns
  background: {
    primary: string
    secondary: string
    elevated: string
    overlay: string
  }
  
  // Text patterns
  text: {
    primary: string
    secondary: string
    muted: string
    inverse: string
  }
  
  // Border patterns
  border: {
    primary: string
    secondary: string
    focus: string
  }
  
  // Interactive patterns
  interactive: {
    hover: string
    active: string
    disabled: string
  }
  
  // Brand patterns
  brand: {
    primary: string
    secondary: string
    accent: string
  }
  
  // Utility patterns
  utils: {
    isDark: boolean
    isLight: boolean
    themeClass: string
  }
}

/**
 * Hook to get unified theme-aware styles
 * Eliminates the need for isDarkMode checks throughout components
 */
export const useThemeStyles = (): ThemeStylePatterns => {
  const { theme, resolvedTheme } = useTheme()
  const tokens = useDesignTokens()
  
  return useMemo((): ThemeStylePatterns => {
    const isDark = resolvedTheme === 'dark'
    const themeColors = tokens.colors.theme
    
    return {
      background: {
        primary: isDark 
          ? 'bg-gray-900' 
          : 'bg-[#e0fbff]',
        secondary: isDark 
          ? 'bg-gray-800' 
          : 'bg-white',
        elevated: isDark 
          ? 'bg-gray-800 border border-gray-700' 
          : 'bg-white border border-gray-200 shadow-sm',
        overlay: isDark 
          ? 'bg-black/70' 
          : 'bg-black/50'
      },
      
      text: {
        primary: isDark 
          ? 'text-white' 
          : 'text-gray-900',
        secondary: isDark 
          ? 'text-gray-300' 
          : 'text-gray-700',
        muted: isDark 
          ? 'text-gray-500' 
          : 'text-gray-500',
        inverse: isDark 
          ? 'text-gray-900' 
          : 'text-white'
      },
      
      border: {
        primary: isDark 
          ? 'border-gray-700' 
          : 'border-gray-200',
        secondary: isDark 
          ? 'border-gray-600' 
          : 'border-gray-300',
        focus: isDark 
          ? 'border-[#00F0FF] ring-[#00F0FF]/20' 
          : 'border-[#00B4DB] ring-[#00B4DB]/20'
      },
      
      interactive: {
        hover: isDark 
          ? 'hover:bg-gray-700' 
          : 'hover:bg-gray-50',
        active: isDark 
          ? 'active:bg-gray-600' 
          : 'active:bg-gray-100',
        disabled: 'opacity-50 cursor-not-allowed'
      },
      
      brand: {
        primary: isDark 
          ? 'text-[#00F0FF]' 
          : 'text-[#00B4DB]',
        secondary: isDark 
          ? 'text-[#48D1CC]' 
          : 'text-[#48D1CC]',
        accent: isDark 
          ? 'text-[#0ea5e9]' 
          : 'text-[#0ea5e9]'
      },
      
      utils: {
        isDark,
        isLight: !isDark,
        themeClass: isDark ? 'dark' : 'light'
      }
    }
  }, [resolvedTheme, tokens])
}

/**
 * Hook for component-specific theme styles
 */
export const useComponentThemeStyles = (component: 'button' | 'card' | 'input' | 'modal') => {
  const baseStyles = useThemeStyles()
  const tokens = useDesignTokens()
  
  return useMemo(() => {
    switch (component) {
      case 'button':
        return {
          ...baseStyles,
          variants: tokens.button.variants,
          sizes: tokens.button.sizes,
          effects: tokens.button.effects
        }
      
      case 'card':
        return {
          ...baseStyles,
          card: {
            base: `${baseStyles.background.elevated} rounded-lg p-6`,
            hover: `${baseStyles.interactive.hover} transition-all duration-200`,
            interactive: `${baseStyles.interactive.hover} cursor-pointer transform hover:scale-[1.02]`
          }
        }
      
      case 'input':
        return {
          ...baseStyles,
          input: {
            base: `${baseStyles.background.secondary} ${baseStyles.border.primary} ${baseStyles.text.primary} rounded-md px-3 py-2 focus:outline-none focus:ring-2`,
            focus: `focus:${baseStyles.border.focus}`,
            error: baseStyles.utils.isDark 
              ? 'border-red-400 ring-red-400/20' 
              : 'border-red-500 ring-red-500/20'
          }
        }
      
      case 'modal':
        return {
          ...baseStyles,
          modal: {
            overlay: `fixed inset-0 z-50 ${baseStyles.background.overlay}`,
            content: `${baseStyles.background.secondary} rounded-lg shadow-xl max-w-md w-full mx-4`,
            header: `${baseStyles.text.primary} text-lg font-semibold`,
            body: `${baseStyles.text.secondary} mt-2`
          }
        }
      
      default:
        return baseStyles
    }
  }, [baseStyles, tokens, component])
}

/**
 * Hook for gradient styles
 */
export const useGradientStyles = () => {
  const { resolvedTheme } = useTheme()
  const tokens = useDesignTokens()
  
  return useMemo(() => {
    const isDark = resolvedTheme === 'dark'
    
    return {
      primary: tokens.colors.gradients.primary,
      secondary: tokens.colors.gradients.secondary,
      accent: tokens.colors.gradients.accent,
      hero: tokens.colors.gradients.hero,
      
      // Text gradients
      text: {
        primary: 'bg-gradient-to-r from-[#00F0FF] to-[#48D1CC] bg-clip-text text-transparent',
        secondary: 'bg-gradient-to-r from-[#48D1CC] to-[#00B4DB] bg-clip-text text-transparent',
        accent: 'bg-gradient-to-r from-[#0ea5e9] to-[#14b8a6] bg-clip-text text-transparent'
      },
      
      // Background gradients
      background: {
        primary: `bg-gradient-to-br ${isDark ? 'from-gray-900 to-gray-800' : 'from-[#e0fbff] to-white'}`,
        card: isDark 
          ? 'bg-gradient-to-br from-gray-800/50 to-gray-900/50' 
          : 'bg-gradient-to-br from-white/80 to-gray-50/80',
        button: tokens.colors.gradients.button
      }
    }
  }, [resolvedTheme, tokens])
}

/**
 * Hook for shadow styles
 */
export const useShadowStyles = () => {
  const { resolvedTheme } = useTheme()
  const tokens = useDesignTokens()
  
  return useMemo(() => {
    const isDark = resolvedTheme === 'dark'
    
    return {
      // Basic shadows
      sm: isDark ? 'shadow-lg shadow-black/20' : tokens.colors.shadows.sm,
      md: isDark ? 'shadow-xl shadow-black/25' : tokens.colors.shadows.md,
      lg: isDark ? 'shadow-2xl shadow-black/30' : tokens.colors.shadows.lg,
      
      // Brand shadows
      primary: isDark 
        ? 'shadow-lg shadow-[#00F0FF]/10' 
        : tokens.colors.shadows.primary,
      secondary: isDark 
        ? 'shadow-lg shadow-[#48D1CC]/10' 
        : tokens.colors.shadows.secondary,
      
      // Glow effects
      glow: {
        primary: isDark 
          ? 'shadow-[0_0_20px_rgba(0,240,255,0.3)]' 
          : tokens.colors.shadows.glow.primary,
        secondary: isDark 
          ? 'shadow-[0_0_20px_rgba(72,209,204,0.3)]' 
          : tokens.colors.shadows.glow.secondary
      }
    }
  }, [resolvedTheme, tokens])
}

/**
 * Utility hook to check theme state
 */
export const useThemeState = () => {
  const { theme, resolvedTheme } = useTheme()
  
  return useMemo(() => ({
    theme,
    resolvedTheme,
    isDark: resolvedTheme === 'dark',
    isLight: resolvedTheme === 'light',
    isSystem: theme === 'system'
  }), [theme, resolvedTheme])
}

export default useThemeStyles
