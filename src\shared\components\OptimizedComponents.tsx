/**
 * ATLAS v2.4 - Optimized Components
 * Performance-optimized component wrappers
 */

import React, { memo, forwardRef } from 'react'
import { motion, MotionProps } from 'framer-motion'

// Optimized motion component
export const OptimizedMotionDiv = memo(
  forwardRef<HTMLDivElement, MotionProps & React.HTMLAttributes<HTMLDivElement>>(
    (props, ref) => <motion.div ref={ref} {...props} />
  )
)

OptimizedMotionDiv.displayName = 'OptimizedMotionDiv'

// Optimized section component
interface OptimizedSectionProps extends React.HTMLAttributes<HTMLElement> {
  children: React.ReactNode
  className?: string
  as?: keyof JSX.IntrinsicElements
}

export const OptimizedSection = memo<OptimizedSectionProps>(({
  children,
  className = '',
  as: Component = 'section',
  ...props
}) => {
  return (
    <Component className={className} {...props}>
      {children}
    </Component>
  )
})

OptimizedSection.displayName = 'OptimizedSection'

// Optimized image component with lazy loading
interface OptimizedImageProps {
  src: string
  alt: string
  className?: string
  width?: number
  height?: number
  priority?: boolean
  onLoad?: () => void
  onError?: () => void
}

export const OptimizedImage = memo<OptimizedImageProps>(({
  src,
  alt,
  className = '',
  width,
  height,
  priority = false,
  onLoad,
  onError,
}) => {
  return (
    <img
      src={src}
      alt={alt}
      className={className}
      width={width}
      height={height}
      loading={priority ? 'eager' : 'lazy'}
      onLoad={onLoad}
      onError={onError}
      decoding="async"
    />
  )
})

OptimizedImage.displayName = 'OptimizedImage'

// Optimized text component
interface OptimizedTextProps extends React.HTMLAttributes<HTMLElement> {
  children: React.ReactNode
  as?: 'p' | 'span' | 'div' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
  className?: string
}

export const OptimizedText = memo<OptimizedTextProps>(({
  children,
  as: Component = 'p',
  className = '',
  ...props
}) => {
  return (
    <Component className={className} {...props}>
      {children}
    </Component>
  )
})

OptimizedText.displayName = 'OptimizedText'

// Optimized grid component
interface OptimizedGridProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  cols?: number
  gap?: string
  className?: string
}

export const OptimizedGrid = memo<OptimizedGridProps>(({
  children,
  cols = 1,
  gap = '1rem',
  className = '',
  ...props
}) => {
  const gridStyle = {
    display: 'grid',
    gridTemplateColumns: `repeat(${cols}, 1fr)`,
    gap,
  }
  
  return (
    <div 
      className={className} 
      style={gridStyle}
      {...props}
    >
      {children}
    </div>
  )
})

OptimizedGrid.displayName = 'OptimizedGrid'

// Optimized list component
interface OptimizedListProps<T> {
  items: T[]
  renderItem: (item: T, index: number) => React.ReactNode
  keyExtractor: (item: T, index: number) => string | number
  className?: string
  as?: 'ul' | 'ol' | 'div'
}

export const OptimizedList = memo(<T,>({
  items,
  renderItem,
  keyExtractor,
  className = '',
  as: Component = 'ul',
}: OptimizedListProps<T>) => {
  return (
    <Component className={className}>
      {items.map((item, index) => (
        <li key={keyExtractor(item, index)}>
          {renderItem(item, index)}
        </li>
      ))}
    </Component>
  )
}) as <T>(props: OptimizedListProps<T>) => JSX.Element

OptimizedList.displayName = 'OptimizedList'

// Optimized conditional render
interface OptimizedConditionalProps {
  condition: boolean
  children: React.ReactNode
  fallback?: React.ReactNode
}

export const OptimizedConditional = memo<OptimizedConditionalProps>(({
  condition,
  children,
  fallback = null,
}) => {
  return condition ? <>{children}</> : <>{fallback}</>
})

OptimizedConditional.displayName = 'OptimizedConditional'

// Performance boundary component
interface PerformanceBoundaryProps {
  children: React.ReactNode
  name?: string
  onRender?: (id: string, phase: string, actualDuration: number) => void
}

export const PerformanceBoundary = memo<PerformanceBoundaryProps>(({
  children,
  name = 'Component',
  onRender,
}) => {
  React.useEffect(() => {
    if (onRender && typeof window !== 'undefined' && 'performance' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.name.includes(name)) {
            onRender(entry.name, 'render', entry.duration)
          }
        })
      })
      
      observer.observe({ entryTypes: ['measure'] })
      
      return () => observer.disconnect()
    }
  }, [name, onRender])
  
  return <>{children}</>
})

PerformanceBoundary.displayName = 'PerformanceBoundary'
