import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import But<PERSON> from '../Button'
import { ThemeProvider } from '@/context/ThemeContext'

// Test wrapper with theme context
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider>{children}</ThemeProvider>
)

describe('Button Component', () => {
  describe('Rendering', () => {
    it('renders with default props', () => {
      render(
        <TestWrapper>
          <Button>Click me</Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button', { name: /click me/i })
      expect(button).toBeInTheDocument()
      expect(button).toHaveClass('inline-flex', 'items-center', 'justify-center')
    })

    it('renders as a link when href is provided', () => {
      render(
        <TestWrapper>
          <Button href="/test">Go to test</Button>
        </TestWrapper>
      )
      
      const link = screen.getByRole('link', { name: /go to test/i })
      expect(link).toBeInTheDocument()
      expect(link).toHaveAttribute('href', '/test')
    })

    it('renders with different variants', () => {
      const { rerender } = render(
        <TestWrapper>
          <Button variant="primary">Primary</Button>
        </TestWrapper>
      )
      
      let button = screen.getByRole('button')
      expect(button).toBeInTheDocument()
      
      rerender(
        <TestWrapper>
          <Button variant="secondary">Secondary</Button>
        </TestWrapper>
      )
      
      button = screen.getByRole('button')
      expect(button).toBeInTheDocument()
    })

    it('renders with different sizes', () => {
      const { rerender } = render(
        <TestWrapper>
          <Button size="sm">Small</Button>
        </TestWrapper>
      )
      
      let button = screen.getByRole('button')
      expect(button).toBeInTheDocument()
      
      rerender(
        <TestWrapper>
          <Button size="lg">Large</Button>
        </TestWrapper>
      )
      
      button = screen.getByRole('button')
      expect(button).toBeInTheDocument()
    })
  })

  describe('Interactions', () => {
    it('handles click events', async () => {
      const handleClick = jest.fn()
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Button onClick={handleClick}>Click me</Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      await user.click(button)
      
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it('does not trigger click when disabled', async () => {
      const handleClick = jest.fn()
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Button onClick={handleClick} disabled>
            Disabled button
          </Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      expect(button).toBeDisabled()
      
      await user.click(button)
      expect(handleClick).not.toHaveBeenCalled()
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(
        <TestWrapper>
          <Button aria-label="Custom label" aria-describedby="description">
            Button
          </Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('aria-label', 'Custom label')
      expect(button).toHaveAttribute('aria-describedby', 'description')
    })

    it('sets aria-disabled when disabled', () => {
      render(
        <TestWrapper>
          <Button disabled>Disabled</Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('aria-disabled', 'true')
    })
  })

  describe('Icons', () => {
    const TestIcon = () => <span data-testid="test-icon">🚀</span>

    it('renders icon on the right by default', () => {
      render(
        <TestWrapper>
          <Button icon={<TestIcon />}>With Icon</Button>
        </TestWrapper>
      )
      
      const icon = screen.getByTestId('test-icon')
      const button = screen.getByRole('button')
      
      expect(icon).toBeInTheDocument()
      expect(button).toContainElement(icon)
    })

    it('renders icon on the left when specified', () => {
      render(
        <TestWrapper>
          <Button icon={<TestIcon />} iconPosition="left">
            With Icon
          </Button>
        </TestWrapper>
      )
      
      const icon = screen.getByTestId('test-icon')
      expect(icon).toBeInTheDocument()
    })
  })

  describe('Theme Integration', () => {
    it('adapts to theme changes', () => {
      render(
        <TestWrapper>
          <Button>Themed Button</Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      expect(button).toBeInTheDocument()
      // Theme-specific styling is handled by CSS classes
      // We verify the component renders without errors
    })
  })
})
