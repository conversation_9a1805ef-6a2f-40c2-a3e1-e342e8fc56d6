'use client';

import { createLazySection, preloadComponent } from '@/components/hoc/withLazyLoading';

/**
 * Lazy-loaded home page components for better performance
 * These components are loaded on-demand to reduce initial bundle size
 */

// Hero Section - Critical, load immediately
export const LazyHeroSection = createLazySection(
  () => import('@/components/home/<USER>'),
  'hero'
);

// Services Section - Important, load with priority
export const LazyServicesSection = createLazySection(
  () => import('@/components/home/<USER>'),
  'services'
);

// About Section - Load when needed
export const LazyAboutSection = createLazySection(
  () => import('@/components/home/<USER>'),
  'services'
);

// Why Choose Us Section - Load when needed
export const LazyWhyChooseUsSection = createLazySection(
  () => import('@/components/home/<USER>'),
  'services'
);

// Process and FAQ Section - Load when needed
export const LazyProcessAndFaqSection = createLazySection(
  () => import('@/components/home/<USER>'),
  'services'
);

// CTA Section - Load when needed
export const LazyCtaSection = createLazySection(
  () => import('@/components/home/<USER>'),
  'contact'
);

/**
 * Preload functions for performance optimization
 * Call these functions to preload components before they're needed
 */
export const preloadHomeComponents = {
  hero: () => preloadComponent(() => import('@/components/home/<USER>')),
  services: () => preloadComponent(() => import('@/components/home/<USER>')),
  about: () => preloadComponent(() => import('@/components/home/<USER>')),
  whyChooseUs: () => preloadComponent(() => import('@/components/home/<USER>')),
  processAndFaq: () => preloadComponent(() => import('@/components/home/<USER>')),
  cta: () => preloadComponent(() => import('@/components/home/<USER>')),
};

/**
 * Preload all non-critical home components
 * Call this function to preload components that are below the fold
 */
export const preloadBelowFoldComponents = async (): Promise<void> => {
  try {
    await Promise.all([
      preloadHomeComponents.about(),
      preloadHomeComponents.whyChooseUs(),
      preloadHomeComponents.processAndFaq(),
      preloadHomeComponents.cta(),
    ]);
  } catch (error) {
    console.warn('Failed to preload some below-fold components:', error);
  }
};

/**
 * Preload critical components
 * Call this function to preload components that are above the fold
 */
export const preloadCriticalComponents = async (): Promise<void> => {
  try {
    await Promise.all([
      preloadHomeComponents.hero(),
      preloadHomeComponents.services(),
    ]);
  } catch (error) {
    console.warn('Failed to preload some critical components:', error);
  }
};
