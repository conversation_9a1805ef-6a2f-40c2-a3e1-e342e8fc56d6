'use client'

import React, { forwardRef, useState, useRef, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Link from 'next/link'
import { cn } from '@/utils/cn'
import { useButtonStyles } from '../../hooks/useDesignTokens'

// Types
export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  // Appearance
  variant?: 'primary' | 'secondary' | 'outline' | 'accent' | 'gradient' | 'ghost'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  
  // State
  loading?: boolean
  disabled?: boolean
  
  // Content
  children: React.ReactNode
  icon?: React.ReactNode
  iconPosition?: 'left' | 'right'
  
  // Effects
  effect?: 'none' | 'ripple' | 'particles' | 'glow'
  animate?: boolean
  
  // Link behavior
  href?: string
  target?: string
  external?: boolean
  
  // Accessibility
  tooltip?: string
  'aria-label'?: string
  
  // Styling
  className?: string
  fullWidth?: boolean
}

/**
 * ATLAS v2.4 Unified Button Component
 * 
 * Consolidates Button.tsx and ButtonWithEffect.tsx functionality
 * Implements design system tokens and consistent styling
 * 
 * Features:
 * - Multiple variants and sizes
 * - Loading and disabled states
 * - Icon support with positioning
 * - Visual effects (ripple, particles, glow)
 * - Link functionality
 * - Full accessibility support
 * - Theme-aware styling
 * 
 * @param props - Button properties
 * @returns Unified button component
 */
const Button = forwardRef<HTMLButtonElement, ButtonProps>(({
  // Appearance
  variant = 'primary',
  size = 'md',
  
  // State
  loading = false,
  disabled = false,
  
  // Content
  children,
  icon,
  iconPosition = 'right',
  
  // Effects
  effect = 'none',
  animate = true,
  
  // Link behavior
  href,
  target,
  external = false,
  
  // Accessibility
  tooltip,
  'aria-label': ariaLabel,
  
  // Styling
  className = '',
  fullWidth = false,
  
  // Events
  onClick,
  onMouseEnter,
  onMouseLeave,
  
  // Rest props
  ...rest
}, ref) => {
  // State for effects
  const [isHovered, setIsHovered] = useState(false)
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([])
  const [particles, setParticles] = useState<Array<{ id: number; x: number; y: number }>>([])
  const buttonRef = useRef<HTMLButtonElement>(null)
  const rippleCounter = useRef(0)
  
  // Get theme-aware styles
  const styles = useButtonStyles(variant, size, disabled, loading)
  
  // Handle ripple effect
  const createRipple = useCallback((event: React.MouseEvent) => {
    if (effect !== 'ripple' || !buttonRef.current) return
    
    const rect = buttonRef.current.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top
    
    const newRipple = {
      id: rippleCounter.current++,
      x,
      y
    }
    
    setRipples(prev => [...prev, newRipple])
    
    // Remove ripple after animation
    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id))
    }, 600)
  }, [effect])
  
  // Handle particle effect
  const createParticles = useCallback((event: React.MouseEvent) => {
    if (effect !== 'particles' || !buttonRef.current) return
    
    const rect = buttonRef.current.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top
    
    // Create multiple particles
    const newParticles = Array.from({ length: 6 }, (_, i) => ({
      id: rippleCounter.current++,
      x: x + (Math.random() - 0.5) * 20,
      y: y + (Math.random() - 0.5) * 20
    }))
    
    setParticles(prev => [...prev, ...newParticles])
    
    // Remove particles after animation
    setTimeout(() => {
      setParticles(prev => prev.filter(particle => 
        !newParticles.some(newP => newP.id === particle.id)
      ))
    }, 800)
  }, [effect])
  
  // Handle click with effects
  const handleClick = useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) return
    
    // Create effects
    createRipple(event)
    createParticles(event)
    
    // Call original onClick
    onClick?.(event)
  }, [disabled, loading, onClick, createRipple, createParticles])
  
  // Handle mouse events
  const handleMouseEnter = useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
    setIsHovered(true)
    onMouseEnter?.(event)
  }, [onMouseEnter])
  
  const handleMouseLeave = useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
    setIsHovered(false)
    onMouseLeave?.(event)
  }, [onMouseLeave])
  
  // Animation variants
  const buttonVariants = {
    initial: { scale: 1 },
    hover: animate ? { 
      scale: 1.02,
      transition: { duration: 0.15, ease: 'easeOut' }
    } : {},
    tap: animate ? { 
      scale: 0.98,
      transition: { duration: 0.1, ease: 'easeInOut' }
    } : {}
  }
  
  // Combine all styles
  const combinedClassName = cn(
    styles.base,
    styles.variant,
    styles.size,
    styles.state,
    {
      'w-full': fullWidth,
      'relative overflow-hidden': effect === 'ripple' || effect === 'particles',
      'shadow-lg': effect === 'glow' && isHovered,
    },
    className
  )
  
  // Loading spinner
  const LoadingSpinner = () => (
    <motion.div
      className="w-4 h-4 border-2 border-current border-t-transparent rounded-full"
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
    />
  )
  
  // Button content
  const ButtonContent = () => (
    <>
      {/* Loading state */}
      {loading && (
        <span className="mr-2">
          <LoadingSpinner />
        </span>
      )}
      
      {/* Icon left */}
      {icon && iconPosition === 'left' && !loading && (
        <span className="mr-2 flex-shrink-0">
          {icon}
        </span>
      )}
      
      {/* Children */}
      <span className={loading ? 'opacity-70' : ''}>
        {children}
      </span>
      
      {/* Icon right */}
      {icon && iconPosition === 'right' && !loading && (
        <span className="ml-2 flex-shrink-0">
          {icon}
        </span>
      )}
      
      {/* Ripple effects */}
      <AnimatePresence>
        {ripples.map(ripple => (
          <motion.span
            key={ripple.id}
            className="absolute bg-white/30 rounded-full pointer-events-none"
            style={{
              left: ripple.x - 10,
              top: ripple.y - 10,
              width: 20,
              height: 20,
            }}
            initial={{ scale: 0, opacity: 0.6 }}
            animate={{ scale: 4, opacity: 0 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.6, ease: 'easeOut' }}
          />
        ))}
      </AnimatePresence>
      
      {/* Particle effects */}
      <AnimatePresence>
        {particles.map(particle => (
          <motion.span
            key={particle.id}
            className="absolute w-1 h-1 bg-current rounded-full pointer-events-none"
            style={{
              left: particle.x,
              top: particle.y,
            }}
            initial={{ scale: 0, opacity: 1 }}
            animate={{
              scale: 1,
              opacity: 0,
              x: (Math.random() - 0.5) * 100,
              y: (Math.random() - 0.5) * 100,
            }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
          />
        ))}
      </AnimatePresence>
    </>
  )
  
  // Render as link if href is provided
  if (href) {
    const linkProps = {
      href,
      target: external ? '_blank' : target,
      rel: external ? 'noopener noreferrer' : undefined,
      className: combinedClassName,
      title: tooltip,
      'aria-label': ariaLabel,
    }
    
    return (
      <motion.div
        variants={buttonVariants}
        initial="initial"
        whileHover="hover"
        whileTap="tap"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <Link {...linkProps}>
          <ButtonContent />
        </Link>
      </motion.div>
    )
  }
  
  // Render as button
  return (
    <motion.button
      ref={(node) => {
        buttonRef.current = node
        if (typeof ref === 'function') {
          ref(node)
        } else if (ref) {
          ref.current = node
        }
      }}
      className={combinedClassName}
      disabled={disabled || loading}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      title={tooltip}
      aria-label={ariaLabel}
      aria-disabled={disabled || loading}
      variants={buttonVariants}
      initial="initial"
      whileHover="hover"
      whileTap="tap"
      {...rest}
    >
      <ButtonContent />
    </motion.button>
  )
})

Button.displayName = 'Button'

export default Button
