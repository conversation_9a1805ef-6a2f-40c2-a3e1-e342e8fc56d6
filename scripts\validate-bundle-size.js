#!/usr/bin/env node

/**
 * ATLAS v2.4 - Bundle Size Validation Script
 * Validates that bundle size stays within ATLAS limits
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Bundle size limits (in bytes)
const LIMITS = {
  total: 1.5 * 1024 * 1024, // 1.5MB
  page: 500 * 1024,         // 500KB per page
  component: 50 * 1024,     // 50KB per component
  chunk: 200 * 1024,        // 200KB per chunk
};

/**
 * Get file size in bytes
 */
function getFileSize(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return stats.size;
  } catch (error) {
    return 0;
  }
}

/**
 * Format bytes to human readable
 */
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Calculate percentage of limit
 */
function getPercentage(size, limit) {
  return ((size / limit) * 100).toFixed(1);
}

/**
 * Get status color based on percentage
 */
function getStatusColor(percentage) {
  if (percentage < 70) return 'green';
  if (percentage < 90) return 'yellow';
  return 'red';
}

/**
 * Analyze Next.js build output
 */
function analyzeBuildOutput() {
  const buildDir = '.next';
  const staticDir = path.join(buildDir, 'static');
  
  if (!fs.existsSync(buildDir)) {
    log('❌ Build directory not found. Run "npm run build" first.', 'red');
    return null;
  }
  
  const analysis = {
    pages: [],
    chunks: [],
    total: 0,
  };
  
  // Analyze pages
  const pagesDir = path.join(staticDir, 'chunks', 'pages');
  if (fs.existsSync(pagesDir)) {
    const pageFiles = fs.readdirSync(pagesDir);
    
    pageFiles.forEach(file => {
      if (file.endsWith('.js')) {
        const filePath = path.join(pagesDir, file);
        const size = getFileSize(filePath);
        
        analysis.pages.push({
          name: file,
          size,
          path: filePath,
        });
        
        analysis.total += size;
      }
    });
  }
  
  // Analyze chunks
  const chunksDir = path.join(staticDir, 'chunks');
  if (fs.existsSync(chunksDir)) {
    const chunkFiles = fs.readdirSync(chunksDir);
    
    chunkFiles.forEach(file => {
      if (file.endsWith('.js') && !file.includes('pages')) {
        const filePath = path.join(chunksDir, file);
        const size = getFileSize(filePath);
        
        analysis.chunks.push({
          name: file,
          size,
          path: filePath,
        });
        
        analysis.total += size;
      }
    });
  }
  
  return analysis;
}

/**
 * Validate bundle sizes
 */
function validateBundleSize() {
  log('\n📦 ATLAS v2.4 Bundle Size Validation', 'blue');
  log('====================================', 'blue');
  
  const analysis = analyzeBuildOutput();
  if (!analysis) {
    return false;
  }
  
  let hasErrors = false;
  let hasWarnings = false;
  
  // Validate total bundle size
  log('\n📊 Total Bundle Size:', 'bold');
  const totalPercentage = getPercentage(analysis.total, LIMITS.total);
  const totalColor = getStatusColor(totalPercentage);
  
  log(`  Total: ${formatBytes(analysis.total)} / ${formatBytes(LIMITS.total)} (${totalPercentage}%)`, totalColor);
  
  if (analysis.total > LIMITS.total) {
    log(`  ❌ Total bundle size exceeds limit!`, 'red');
    hasErrors = true;
  } else if (analysis.total > LIMITS.total * 0.9) {
    log(`  ⚠️  Total bundle size approaching limit`, 'yellow');
    hasWarnings = true;
  } else {
    log(`  ✅ Total bundle size within limits`, 'green');
  }
  
  // Validate page sizes
  log('\n📄 Page Sizes:', 'bold');
  analysis.pages.forEach(page => {
    const percentage = getPercentage(page.size, LIMITS.page);
    const color = getStatusColor(percentage);
    
    log(`  ${page.name}: ${formatBytes(page.size)} (${percentage}%)`, color);
    
    if (page.size > LIMITS.page) {
      log(`    ❌ Page exceeds size limit!`, 'red');
      hasErrors = true;
    } else if (page.size > LIMITS.page * 0.9) {
      log(`    ⚠️  Page approaching size limit`, 'yellow');
      hasWarnings = true;
    }
  });
  
  // Validate chunk sizes
  log('\n🧩 Chunk Sizes:', 'bold');
  analysis.chunks.forEach(chunk => {
    const percentage = getPercentage(chunk.size, LIMITS.chunk);
    const color = getStatusColor(percentage);
    
    log(`  ${chunk.name}: ${formatBytes(chunk.size)} (${percentage}%)`, color);
    
    if (chunk.size > LIMITS.chunk) {
      log(`    ❌ Chunk exceeds size limit!`, 'red');
      hasErrors = true;
    } else if (chunk.size > LIMITS.chunk * 0.9) {
      log(`    ⚠️  Chunk approaching size limit`, 'yellow');
      hasWarnings = true;
    }
  });
  
  // Largest files analysis
  log('\n📈 Largest Files:', 'bold');
  const allFiles = [...analysis.pages, ...analysis.chunks];
  const sortedFiles = allFiles.sort((a, b) => b.size - a.size).slice(0, 5);
  
  sortedFiles.forEach((file, index) => {
    log(`  ${index + 1}. ${file.name}: ${formatBytes(file.size)}`, 'blue');
  });
  
  // Recommendations
  log('\n💡 Optimization Recommendations:', 'blue');
  
  if (analysis.total > LIMITS.total * 0.8) {
    log('  • Consider implementing more aggressive code splitting', 'blue');
    log('  • Review and remove unused dependencies', 'blue');
    log('  • Implement dynamic imports for large components', 'blue');
  }
  
  if (analysis.pages.some(p => p.size > LIMITS.page * 0.8)) {
    log('  • Break down large pages into smaller components', 'blue');
    log('  • Implement lazy loading for non-critical components', 'blue');
  }
  
  if (analysis.chunks.some(c => c.size > LIMITS.chunk * 0.8)) {
    log('  • Review chunk splitting configuration', 'blue');
    log('  • Consider splitting large libraries into separate chunks', 'blue');
  }
  
  // Summary
  log('\n📋 Summary:', 'bold');
  
  if (hasErrors) {
    log('❌ Bundle size validation failed - size limits exceeded', 'red');
    log('🔧 Action required: Optimize bundle size before deployment', 'red');
    return false;
  } else if (hasWarnings) {
    log('⚠️  Bundle size validation passed with warnings', 'yellow');
    log('💡 Recommendation: Monitor and optimize bundle size', 'yellow');
    return true;
  } else {
    log('✅ Bundle size validation passed - all sizes within limits', 'green');
    log('🎉 Bundle is optimized and ready for deployment', 'green');
    return true;
  }
}

// Performance budget check
function checkPerformanceBudget() {
  log('\n⚡ Performance Budget Check:', 'bold');
  
  const budgets = {
    'First Contentful Paint': '1.5s',
    'Largest Contentful Paint': '2.5s',
    'Cumulative Layout Shift': '0.1',
    'First Input Delay': '100ms',
  };
  
  Object.entries(budgets).forEach(([metric, budget]) => {
    log(`  ${metric}: Target < ${budget}`, 'blue');
  });
  
  log('  💡 Run "npm run lighthouse" to measure actual performance', 'blue');
}

// Run validation
try {
  const success = validateBundleSize();
  checkPerformanceBudget();
  
  if (success) {
    log('\n✅ Bundle size validation completed successfully!', 'green');
    process.exit(0);
  } else {
    log('\n❌ Bundle size validation failed!', 'red');
    process.exit(1);
  }
} catch (error) {
  log(`❌ Error during bundle size validation: ${error.message}`, 'red');
  process.exit(1);
}
