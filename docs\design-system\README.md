# ATLAS v2.4 Design System

## Visión General

El Design System ATLAS v2.4 es un sistema de diseño completo y unificado que proporciona componentes reutilizables, tokens de diseño consistentes, y patrones de desarrollo estandarizados para InformatiK-AI.

## 🎯 Objetivos

- **Consistencia**: Experiencia de usuario uniforme en toda la aplicación
- **Eficiencia**: Desarrollo acelerado con componentes reutilizables
- **Escalabilidad**: Arquitectura modular que crece con el proyecto
- **Accesibilidad**: WCAG 2.1 AA compliance en todos los componentes
- **Performance**: Optimización automática y lazy loading

## 📦 Componentes Disponibles

### Core Components

| Componente | Versión | Variantes | Tests | Storybook | Estado |
|------------|---------|-----------|-------|-----------|--------|
| **Button** | 2.1.0 | 6 variantes | ✅ 95% | ✅ 20+ stories | Estable |
| **Card** | 3.1.0 | 5 variantes | ✅ 90% | ✅ 15+ stories | Estable |
| **Input** | 3.1.0 | 4 variantes | ✅ 88% | ✅ 12+ stories | Estable |
| **Modal** | 3.1.0 | 4 variantes | ✅ 85% | ✅ 10+ stories | Estable |

### Utility Components

| Componente | Descripción | Estado |
|------------|-------------|--------|
| **LazyComponents** | Sistema de lazy loading | ✅ Estable |
| **OptimizedComponents** | Componentes memoizados | ✅ Estable |
| **ThemeProvider** | Gestión de temas | ✅ Estable |

## 🎨 Design Tokens

### Colores

```typescript
// Colores primarios
primary: {
  50: '#E6F9FF',
  100: '#B3EFFF', 
  500: '#00B4DB',  // Principal
  600: '#0099C2',
  900: '#004D61'
}

// Colores secundarios
secondary: {
  50: '#E6FFFF',
  100: '#B3FFFF',
  500: '#48D1CC',  // Secundario
  600: '#3BB8B3',
  900: '#1D5C5A'
}
```

### Tipografía

```typescript
// Familias de fuentes
fontFamily: {
  sans: ['Inter', 'system-ui', 'sans-serif'],
  mono: ['JetBrains Mono', 'monospace']
}

// Tamaños
fontSize: {
  xs: '0.75rem',    // 12px
  sm: '0.875rem',   // 14px
  base: '1rem',     // 16px
  lg: '1.125rem',   // 18px
  xl: '1.25rem',    // 20px
  '2xl': '1.5rem',  // 24px
  '3xl': '1.875rem' // 30px
}
```

### Espaciado

```typescript
spacing: {
  1: '0.25rem',   // 4px
  2: '0.5rem',    // 8px
  3: '0.75rem',   // 12px
  4: '1rem',      // 16px
  6: '1.5rem',    // 24px
  8: '2rem',      // 32px
  12: '3rem',     // 48px
  16: '4rem'      // 64px
}
```

### Animaciones

```typescript
animation: {
  'fade-in': 'fadeIn 0.3s ease-out',
  'slide-up': 'slideUp 0.3s ease-out',
  'scale-in': 'scaleIn 0.2s ease-out',
  'spin': 'spin 1s linear infinite'
}
```

## 🚀 Instalación y Uso

### Instalación

```bash
# Instalar dependencias
npm install

# Iniciar Storybook para desarrollo
npm run storybook

# Ejecutar tests
npm run test:ui-system
```

### Uso Básico

```typescript
import { Button, Card, Input, Modal } from '@/shared/ui-system'

// Uso de componentes
function MyComponent() {
  return (
    <Card variant="elevated" size="md">
      <Input 
        label="Email"
        type="email"
        variant="outlined"
        required
      />
      <Button 
        variant="gradient"
        size="lg"
        onClick={handleSubmit}
      >
        Enviar
      </Button>
    </Card>
  )
}
```

### Uso de Hooks

```typescript
import { useThemeStyles, useDesignTokens } from '@/shared/ui-system'

function ThemedComponent() {
  const themeStyles = useThemeStyles()
  const tokens = useDesignTokens()
  
  return (
    <div className={themeStyles.background.primary}>
      <h1 style={{ color: tokens.colors.primary[500] }}>
        Título con tema
      </h1>
    </div>
  )
}
```

## 📱 Responsive Design

### Breakpoints

```typescript
screens: {
  sm: '640px',   // Mobile landscape
  md: '768px',   // Tablet
  lg: '1024px',  // Desktop
  xl: '1280px',  // Large desktop
  '2xl': '1536px' // Extra large
}
```

### Uso Responsive

```typescript
// Clases responsive
<Card className="w-full md:w-1/2 lg:w-1/3">
  Contenido responsive
</Card>

// Hooks responsive
const { isMobile, isTablet, isDesktop } = useBreakpoint()
```

## ♿ Accesibilidad

### Estándares

- **WCAG 2.1 AA**: Compliance completo
- **Contraste**: Mínimo 4.5:1 para texto normal
- **Navegación**: Soporte completo de teclado
- **Screen Readers**: ARIA labels y roles apropiados

### Validación

```bash
# Validar accesibilidad
npm run validate:accessibility

# Tests de accesibilidad
npm run test:a11y
```

## 🎭 Temas

### Tema Claro

```typescript
light: {
  background: {
    primary: 'bg-white',
    secondary: 'bg-gray-50'
  },
  text: {
    primary: 'text-gray-900',
    secondary: 'text-gray-600'
  }
}
```

### Tema Oscuro

```typescript
dark: {
  background: {
    primary: 'bg-gray-900',
    secondary: 'bg-gray-800'
  },
  text: {
    primary: 'text-white',
    secondary: 'text-gray-300'
  }
}
```

## 🔧 Personalización

### Extender Componentes

```typescript
// Crear variante personalizada
const CustomButton = styled(Button)`
  // Estilos personalizados
`

// O usando className
<Button className="custom-styles">
  Botón personalizado
</Button>
```

### Nuevos Tokens

```typescript
// Extender tokens existentes
const customTokens = {
  ...defaultTokens,
  colors: {
    ...defaultTokens.colors,
    brand: {
      primary: '#FF6B6B',
      secondary: '#4ECDC4'
    }
  }
}
```

## 📊 Performance

### Métricas

- **Bundle Size**: <50KB por componente
- **Tree Shaking**: 100% compatible
- **Lazy Loading**: Automático
- **Memoización**: Optimizada

### Optimizaciones

```typescript
// Importación optimizada
import { Button } from '@/shared/ui-system/components/Button'

// En lugar de
import { Button } from '@/shared/ui-system' // Importa todo
```

## 🧪 Testing

### Estrategia de Testing

1. **Unit Tests**: Cada componente >85% cobertura
2. **Integration Tests**: Interacciones entre componentes
3. **Visual Tests**: Storybook visual regression
4. **Accessibility Tests**: Validación WCAG automática

### Ejecutar Tests

```bash
# Tests unitarios
npm run test:unit

# Tests de integración
npm run test:integration

# Tests visuales
npm run test:visual

# Todos los tests
npm run test:all
```

## 📚 Recursos

### Documentación

- [Guía de Componentes](./components/README.md)
- [Tokens de Diseño](./tokens/README.md)
- [Patrones de Uso](./patterns/README.md)
- [Migración Legacy](./migration/README.md)

### Herramientas

- **Storybook**: Documentación visual interactiva
- **Figma**: Diseños y especificaciones
- **Chromatic**: Visual regression testing

### Soporte

- **Issues**: GitHub Issues para bugs y features
- **Discussions**: GitHub Discussions para preguntas
- **Wiki**: Documentación extendida

## 🔄 Versionado

### Semantic Versioning

- **Major**: Cambios breaking (2.0.0 → 3.0.0)
- **Minor**: Nuevas features (2.1.0 → 2.2.0)
- **Patch**: Bug fixes (2.1.0 → 2.1.1)

### Changelog

Ver [CHANGELOG.md](../../CHANGELOG.md) para historial completo de cambios.

## 🤝 Contribución

### Guidelines

1. Seguir patrones ATLAS v2.4
2. Mantener >85% test coverage
3. Documentar en Storybook
4. Validar accesibilidad
5. Optimizar performance

### Proceso

1. Fork del repositorio
2. Crear feature branch
3. Implementar cambios
4. Ejecutar quality gates
5. Crear Pull Request

---

**ATLAS v2.4 Design System** - Construyendo el futuro de InformatiK-AI con componentes consistentes, accesibles y performantes.
