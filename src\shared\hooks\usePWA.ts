/**
 * ATLAS v2.4 - PWA Hook
 * Progressive Web App functionality and service worker management
 */

import { useState, useEffect, useCallback } from 'react'

// Types
interface PWAInstallPrompt {
  prompt: () => Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

interface PWAState {
  isInstallable: boolean
  isInstalled: boolean
  isOnline: boolean
  isUpdateAvailable: boolean
  registration: ServiceWorkerRegistration | null
}

interface PWAActions {
  install: () => Promise<void>
  update: () => Promise<void>
  unregister: () => Promise<boolean>
  showInstallPrompt: () => void
  dismissInstallPrompt: () => void
}

/**
 * Custom hook for PWA functionality
 * 
 * Features:
 * - Service worker registration and management
 * - Install prompt handling
 * - Online/offline detection
 * - Update notifications
 * - Installation status tracking
 * 
 * @returns PWA state and actions
 */
export const usePWA = (): PWAState & PWAActions => {
  const [isInstallable, setIsInstallable] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)
  const [isOnline, setIsOnline] = useState(true)
  const [isUpdateAvailable, setIsUpdateAvailable] = useState(false)
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null)
  const [installPrompt, setInstallPrompt] = useState<PWAInstallPrompt | null>(null)
  
  // Check if app is installed
  const checkInstallStatus = useCallback(() => {
    if (typeof window !== 'undefined') {
      // Check for standalone mode (iOS)
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches
      
      // Check for installed PWA (Android)
      const isInstalled = window.navigator && 'getInstalledRelatedApps' in window.navigator
      
      setIsInstalled(isStandalone || Boolean(isInstalled))
    }
  }, [])
  
  // Register service worker
  const registerServiceWorker = useCallback(async () => {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js', {
          scope: '/',
          updateViaCache: 'none'
        })
        
        setRegistration(registration)
        
        // Check for updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                setIsUpdateAvailable(true)
              }
            })
          }
        })
        
        // Listen for controlling service worker changes
        navigator.serviceWorker.addEventListener('controllerchange', () => {
          window.location.reload()
        })
        
        console.log('[PWA] Service worker registered successfully')
        return registration
      } catch (error) {
        console.error('[PWA] Service worker registration failed:', error)
        return null
      }
    }
    return null
  }, [])
  
  // Handle install prompt
  const handleInstallPrompt = useCallback((event: Event) => {
    event.preventDefault()
    setInstallPrompt(event as any)
    setIsInstallable(true)
  }, [])
  
  // Install PWA
  const install = useCallback(async () => {
    if (installPrompt) {
      try {
        await installPrompt.prompt()
        const choiceResult = await installPrompt.userChoice
        
        if (choiceResult.outcome === 'accepted') {
          setIsInstalled(true)
          setIsInstallable(false)
          console.log('[PWA] App installed successfully')
        } else {
          console.log('[PWA] App installation dismissed')
        }
        
        setInstallPrompt(null)
      } catch (error) {
        console.error('[PWA] Installation failed:', error)
      }
    }
  }, [installPrompt])
  
  // Update service worker
  const update = useCallback(async () => {
    if (registration && registration.waiting) {
      try {
        registration.waiting.postMessage({ type: 'SKIP_WAITING' })
        setIsUpdateAvailable(false)
        console.log('[PWA] App updated successfully')
      } catch (error) {
        console.error('[PWA] Update failed:', error)
      }
    }
  }, [registration])
  
  // Unregister service worker
  const unregister = useCallback(async (): Promise<boolean> => {
    if (registration) {
      try {
        const result = await registration.unregister()
        setRegistration(null)
        console.log('[PWA] Service worker unregistered')
        return result
      } catch (error) {
        console.error('[PWA] Unregistration failed:', error)
        return false
      }
    }
    return false
  }, [registration])
  
  // Show install prompt
  const showInstallPrompt = useCallback(() => {
    if (installPrompt) {
      install()
    }
  }, [install, installPrompt])
  
  // Dismiss install prompt
  const dismissInstallPrompt = useCallback(() => {
    setIsInstallable(false)
    setInstallPrompt(null)
  }, [])
  
  // Handle online/offline status
  const handleOnlineStatus = useCallback(() => {
    setIsOnline(navigator.onLine)
  }, [])
  
  // Initialize PWA functionality
  useEffect(() => {
    if (typeof window === 'undefined') return
    
    // Check initial install status
    checkInstallStatus()
    
    // Register service worker
    registerServiceWorker()
    
    // Set initial online status
    setIsOnline(navigator.onLine)
    
    // Add event listeners
    window.addEventListener('beforeinstallprompt', handleInstallPrompt)
    window.addEventListener('appinstalled', () => {
      setIsInstalled(true)
      setIsInstallable(false)
      console.log('[PWA] App installed via browser')
    })
    window.addEventListener('online', handleOnlineStatus)
    window.addEventListener('offline', handleOnlineStatus)
    
    // Listen for display mode changes
    const mediaQuery = window.matchMedia('(display-mode: standalone)')
    const handleDisplayModeChange = (e: MediaQueryListEvent) => {
      setIsInstalled(e.matches)
    }
    
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleDisplayModeChange)
    } else {
      // Fallback for older browsers
      mediaQuery.addListener(handleDisplayModeChange)
    }
    
    // Cleanup
    return () => {
      window.removeEventListener('beforeinstallprompt', handleInstallPrompt)
      window.removeEventListener('online', handleOnlineStatus)
      window.removeEventListener('offline', handleOnlineStatus)
      
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', handleDisplayModeChange)
      } else {
        mediaQuery.removeListener(handleDisplayModeChange)
      }
    }
  }, [checkInstallStatus, registerServiceWorker, handleInstallPrompt, handleOnlineStatus])
  
  return {
    // State
    isInstallable,
    isInstalled,
    isOnline,
    isUpdateAvailable,
    registration,
    
    // Actions
    install,
    update,
    unregister,
    showInstallPrompt,
    dismissInstallPrompt,
  }
}

export default usePWA
