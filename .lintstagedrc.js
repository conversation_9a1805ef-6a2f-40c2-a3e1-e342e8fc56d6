module.exports = {
  // TypeScript and JavaScript files
  '*.{js,jsx,ts,tsx}': [
    'eslint --fix',
    'prettier --write',
    'jest --bail --findRelatedTests --passWithNoTests',
  ],
  
  // JSON, CSS, and Markdown files
  '*.{json,css,md}': [
    'prettier --write',
  ],
  
  // Package.json
  'package.json': [
    'prettier --write',
  ],
  
  // TypeScript type checking for staged files
  '*.{ts,tsx}': () => 'tsc --noEmit',
}
