'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { useNeuralAnimation } from '../../hooks/useNeuralAnimation'
import { useThemeStyles } from '@/shared/ui-system'

interface NeuralBackgroundProps {
  className?: string
  pointCount?: number
  lineCount?: number
}

/**
 * Neural Network Background Component
 * Animated background with neural network visualization
 * Extracted from contact/page.tsx for reusability
 */
const NeuralBackground: React.FC<NeuralBackgroundProps> = ({
  className = '',
  pointCount = 15,
  lineCount = 20,
}) => {
  const themeStyles = useThemeStyles()
  const { neuralPoints, neuralLines, isInitialized } = useNeuralAnimation({
    pointCount,
    lineCount,
  })
  
  // Don't render until initialized (client-side only)
  if (!isInitialized) {
    return null
  }
  
  return (
    <div className={`absolute inset-0 overflow-hidden ${className}`}>
      <svg
        className="absolute inset-0 w-full h-full"
        viewBox="0 0 100 100"
        preserveAspectRatio="xMidYMid slice"
      >
        {/* Neural network lines */}
        <g className="neural-lines">
          {neuralLines.map((line, index) => (
            <motion.line
              key={`line-${index}`}
              x1={`${line.x1}%`}
              y1={`${line.y1}%`}
              x2={`${line.x2}%`}
              y2={`${line.y2}%`}
              stroke={themeStyles.utils.isDark ? 'rgba(0, 240, 255, 0.1)' : 'rgba(0, 180, 219, 0.15)'}
              strokeWidth="0.1"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={{ 
                pathLength: 1, 
                opacity: [0, 0.3, 0],
              }}
              transition={{
                duration: 4 + Math.random() * 4,
                repeat: Infinity,
                delay: Math.random() * 2,
                ease: 'easeInOut',
              }}
            />
          ))}
        </g>
        
        {/* Neural network points */}
        <g className="neural-points">
          {neuralPoints.map((point, index) => (
            <motion.circle
              key={`point-${index}`}
              cx={`${point.cx}%`}
              cy={`${point.cy}%`}
              r={point.r}
              fill={themeStyles.utils.isDark ? '#00F0FF' : '#00B4DB'}
              initial={{ scale: 0, opacity: 0 }}
              animate={{ 
                scale: [0, 1, 1.2, 1],
                opacity: [0, 0.6, 0.8, 0.4],
              }}
              transition={{
                duration: parseFloat(point.duration),
                repeat: Infinity,
                delay: Math.random() * 2,
                ease: 'easeInOut',
              }}
            />
          ))}
        </g>
        
        {/* Animated gradient overlay */}
        <defs>
          <radialGradient id="neuralGradient" cx="50%" cy="50%" r="50%">
            <stop 
              offset="0%" 
              stopColor={themeStyles.utils.isDark ? 'rgba(0, 240, 255, 0.1)' : 'rgba(0, 180, 219, 0.1)'} 
            />
            <stop 
              offset="100%" 
              stopColor="transparent" 
            />
          </radialGradient>
        </defs>
        
        <motion.circle
          cx="50%"
          cy="50%"
          r="30%"
          fill="url(#neuralGradient)"
          initial={{ scale: 0.8, opacity: 0.3 }}
          animate={{ 
            scale: [0.8, 1.2, 0.8],
            opacity: [0.3, 0.1, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
      </svg>
    </div>
  )
}

export default NeuralBackground
