#!/usr/bin/env node

/**
 * ATLAS v2.4 - Design System Migration Script
 * Automatically migrates components to use the unified design system
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Migration patterns
const migrationPatterns = [
  // Button imports migration
  {
    name: 'Button imports',
    pattern: /import\s+.*Button.*\s+from\s+['"]@\/components\/ui\/Button['"]/g,
    replacement: "import { Button } from '@/shared/ui-system'",
    description: 'Migrate Button imports to unified design system'
  },
  {
    name: 'ButtonWithEffect imports',
    pattern: /import\s+.*ButtonWithEffect.*\s+from\s+['"]@\/components\/ui\/ButtonWithEffect['"]/g,
    replacement: "import { Button } from '@/shared/ui-system'",
    description: 'Migrate ButtonWithEffect imports to unified Button'
  },
  
  // Theme hook migration
  {
    name: 'isDarkMode pattern',
    pattern: /const\s+{\s*theme\s*}\s*=\s*useTheme\(\)\s*;\s*const\s+isDarkMode\s*=\s*theme\s*===\s*['"]dark['"]/g,
    replacement: "const themeStyles = useThemeStyles()",
    description: 'Replace isDarkMode pattern with useThemeStyles hook'
  },
  {
    name: 'useTheme isDarkMode',
    pattern: /const\s+isDarkMode\s*=\s*theme\s*===\s*['"]dark['"]/g,
    replacement: "const { utils: { isDark } } = useThemeStyles()",
    description: 'Replace isDarkMode with themeStyles.utils.isDark'
  },
  
  // Hardcoded colors migration
  {
    name: 'Hardcoded primary colors',
    pattern: /#00B4DB/g,
    replacement: 'themeStyles.brand.primary',
    description: 'Replace hardcoded primary color with design token'
  },
  {
    name: 'Hardcoded secondary colors',
    pattern: /#48D1CC/g,
    replacement: 'themeStyles.brand.secondary',
    description: 'Replace hardcoded secondary color with design token'
  },
  {
    name: 'Hardcoded accent colors',
    pattern: /#00F0FF/g,
    replacement: 'themeStyles.brand.accent',
    description: 'Replace hardcoded accent color with design token'
  },
  
  // Background patterns
  {
    name: 'Dark background pattern',
    pattern: /bg-black|bg-gray-900/g,
    replacement: 'themeStyles.background.primary',
    description: 'Replace hardcoded dark backgrounds with design token'
  },
  {
    name: 'Light background pattern',
    pattern: /bg-\[#E0FBFF\]|bg-\[#e0fbff\]/g,
    replacement: 'themeStyles.background.primary',
    description: 'Replace hardcoded light backgrounds with design token'
  },
  
  // Text color patterns
  {
    name: 'Dark text pattern',
    pattern: /text-white/g,
    replacement: 'themeStyles.text.primary',
    description: 'Replace hardcoded white text with design token'
  },
  {
    name: 'Light text pattern',
    pattern: /text-\[#111111\]|text-gray-900/g,
    replacement: 'themeStyles.text.primary',
    description: 'Replace hardcoded dark text with design token'
  },
];

// Import additions needed
const requiredImports = [
  {
    pattern: /useThemeStyles/,
    import: "import { useThemeStyles } from '@/shared/ui-system'"
  },
  {
    pattern: /Button/,
    import: "import { Button } from '@/shared/ui-system'"
  }
];

/**
 * Migrate a single file
 */
function migrateFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    const appliedMigrations = [];
    
    // Apply migration patterns
    migrationPatterns.forEach(pattern => {
      const originalContent = content;
      content = content.replace(pattern.pattern, pattern.replacement);
      
      if (content !== originalContent) {
        hasChanges = true;
        appliedMigrations.push(pattern.name);
      }
    });
    
    // Add required imports if patterns were found
    if (hasChanges) {
      requiredImports.forEach(({ pattern, import: importStatement }) => {
        if (pattern.test(content) && !content.includes(importStatement)) {
          // Find the last import statement
          const importRegex = /import\s+.*from\s+['"][^'"]*['"];?\s*\n/g;
          const imports = content.match(importRegex);
          
          if (imports && imports.length > 0) {
            const lastImport = imports[imports.length - 1];
            const lastImportIndex = content.lastIndexOf(lastImport);
            const insertIndex = lastImportIndex + lastImport.length;
            
            content = content.slice(0, insertIndex) + 
                     importStatement + '\n' + 
                     content.slice(insertIndex);
          } else {
            // Add import at the beginning after 'use client' if present
            const useClientMatch = content.match(/['"]use client['"];?\s*\n/);
            if (useClientMatch) {
              const insertIndex = useClientMatch.index + useClientMatch[0].length;
              content = content.slice(0, insertIndex) + 
                       '\n' + importStatement + '\n' + 
                       content.slice(insertIndex);
            } else {
              content = importStatement + '\n\n' + content;
            }
          }
        }
      });
    }
    
    // Write back if changes were made
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      log(`  ✅ ${path.relative(process.cwd(), filePath)}`, 'green');
      appliedMigrations.forEach(migration => {
        log(`    • ${migration}`, 'blue');
      });
      return { migrated: true, patterns: appliedMigrations };
    }
    
    return { migrated: false, patterns: [] };
  } catch (error) {
    log(`  ❌ Error migrating ${filePath}: ${error.message}`, 'red');
    return { migrated: false, patterns: [], error: error.message };
  }
}

/**
 * Find all TypeScript/JavaScript files to migrate
 */
function findFilesToMigrate() {
  const extensions = ['.tsx', '.ts', '.jsx', '.js'];
  const excludeDirs = ['node_modules', '.next', 'dist', 'build', '.git'];
  const files = [];
  
  function scanDirectory(dir) {
    try {
      const items = fs.readdirSync(dir);
      
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !excludeDirs.includes(item)) {
          scanDirectory(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          // Skip already migrated files
          if (!fullPath.includes('src/shared/ui-system/') && 
              !fullPath.includes('scripts/') &&
              !fullPath.includes('.test.') &&
              !fullPath.includes('.spec.')) {
            files.push(fullPath);
          }
        }
      });
    } catch (error) {
      // Ignore permission errors
    }
  }
  
  scanDirectory('src');
  return files;
}

/**
 * Main migration function
 */
function runMigration() {
  log('\n🔄 ATLAS v2.4 Design System Migration', 'blue');
  log('=====================================', 'blue');
  
  const files = findFilesToMigrate();
  log(`\n📁 Found ${files.length} files to analyze...`, 'bold');
  
  let migratedCount = 0;
  let totalPatterns = 0;
  const migrationSummary = {};
  
  files.forEach(file => {
    const result = migrateFile(file);
    
    if (result.migrated) {
      migratedCount++;
      totalPatterns += result.patterns.length;
      
      result.patterns.forEach(pattern => {
        migrationSummary[pattern] = (migrationSummary[pattern] || 0) + 1;
      });
    }
  });
  
  // Summary
  log('\n📊 Migration Summary:', 'bold');
  log('===================', 'blue');
  
  if (migratedCount === 0) {
    log('✨ No files needed migration - all components are up to date!', 'green');
  } else {
    log(`✅ Successfully migrated ${migratedCount} files`, 'green');
    log(`🔧 Applied ${totalPatterns} migration patterns`, 'green');
    
    log('\n📋 Migration Patterns Applied:', 'bold');
    Object.entries(migrationSummary).forEach(([pattern, count]) => {
      log(`  • ${pattern}: ${count} files`, 'blue');
    });
  }
  
  // Recommendations
  log('\n💡 Next Steps:', 'blue');
  log('1. Run tests to ensure functionality is preserved', 'blue');
  log('2. Check for any remaining hardcoded styles manually', 'blue');
  log('3. Update Storybook stories if needed', 'blue');
  log('4. Run the design system validation script', 'blue');
  
  log('\n✅ Migration completed successfully!', 'green');
}

// Run migration
try {
  runMigration();
} catch (error) {
  log(`❌ Migration failed: ${error.message}`, 'red');
  process.exit(1);
}
