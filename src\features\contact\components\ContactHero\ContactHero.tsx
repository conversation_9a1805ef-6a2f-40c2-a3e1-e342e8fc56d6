'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { useThemeStyles } from '@/shared/ui-system'
import NeuralBackground from '../NeuralBackground/NeuralBackground'

interface ContactHeroProps {
  className?: string
}

/**
 * Contact Hero Section Component
 * Extracted from contact/page.tsx following ATLAS v2.4 principles
 */
const ContactHero: React.FC<ContactHeroProps> = ({ className = '' }) => {
  const themeStyles = useThemeStyles()
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  }
  
  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' },
    },
  }
  
  return (
    <section className={`relative py-24 sm:py-28 md:py-36 lg:py-44 overflow-hidden ${themeStyles.background.primary} ${className}`}>
      {/* Neural Network Background */}
      <NeuralBackground />
      
      {/* Glow effects */}
      <div className="absolute top-0 left-1/4 w-1/2 h-1/3 rounded-full filter blur-[120px] bg-[#00B4DB]/20" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full filter blur-[150px] bg-[#48D1CC]/15" />
      
      {/* Grid pattern overlay */}
      <div className={`absolute inset-0 ${themeStyles.utils.isDark ? 'bg-grid-white/[0.03]' : 'bg-grid-slate-300/[0.2]'} bg-[length:30px_30px]`} />
      
      {/* Content */}
      <div className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="max-w-4xl mx-auto text-center"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Main Title */}
          <motion.div variants={itemVariants}>
            <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-extrabold leading-tight mb-6">
              <span className={`block ${themeStyles.text.primary}`}>
                Conecta con
              </span>
              <span className="block">
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#00F0FF] to-[#48D1CC]">
                  Nuestro Equipo
                </span>
              </span>
              <span className="block">
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#00B4DB] via-[#48D1CC] to-[#00BFFF]">
                  Estamos aquí para ayudarte
                </span>
              </span>
            </h1>
          </motion.div>
          
          {/* Subtitle */}
          <motion.p
            className={`text-xl md:text-2xl mb-14 ${themeStyles.text.secondary} max-w-3xl mx-auto tech-text`}
            variants={itemVariants}
          >
            ¿Tienes preguntas o estás listo para comenzar tu viaje con IA?
            Nuestro equipo está listo para responder a todas tus consultas.
          </motion.p>
        </motion.div>
      </div>
    </section>
  )
}

export default ContactHero
