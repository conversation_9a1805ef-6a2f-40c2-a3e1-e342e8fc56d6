import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'
import { <PERSON>, Star, Settings, ArrowRight } from 'lucide-react'

import Card from './Card'
import { Button } from '@/shared/ui-system'
import { ThemeProvider } from '@/context/ThemeContext'

const meta: Meta<typeof Card> = {
  title: 'Design System/Card',
  component: Card,
  decorators: [
    (Story) => (
      <ThemeProvider>
        <div className="p-4 max-w-md">
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'ATLAS v2.4 Card Component. Versatile card component with multiple variants, animations, and full accessibility support.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'elevated', 'outlined', 'filled', 'glass'],
      description: 'Visual style variant of the card',
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg', 'xl'],
      description: 'Size of the card',
    },
    padding: {
      control: 'select',
      options: ['none', 'sm', 'md', 'lg', 'xl'],
      description: 'Internal padding of the card',
    },
    radius: {
      control: 'select',
      options: ['none', 'sm', 'md', 'lg', 'xl', 'full'],
      description: 'Border radius of the card',
    },
    interactive: {
      control: 'boolean',
      description: 'Whether the card is interactive',
    },
    loading: {
      control: 'boolean',
      description: 'Whether the card is in loading state',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the card is disabled',
    },
    animate: {
      control: 'boolean',
      description: 'Whether to show animations',
    },
    hover: {
      control: 'boolean',
      description: 'Whether to show hover effects',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

// Basic variants
export const Default: Story = {
  args: {
    children: 'This is a default card with some content.',
  },
}

export const Elevated: Story = {
  args: {
    variant: 'elevated',
    children: 'This is an elevated card with shadow effects.',
  },
}

export const Outlined: Story = {
  args: {
    variant: 'outlined',
    children: 'This is an outlined card with border styling.',
  },
}

export const Filled: Story = {
  args: {
    variant: 'filled',
    children: 'This is a filled card with background color.',
  },
}

export const Glass: Story = {
  args: {
    variant: 'glass',
    children: 'This is a glass card with backdrop blur effect.',
  },
}

// Size variations
export const Small: Story = {
  args: {
    size: 'sm',
    children: 'Small card content',
  },
}

export const Medium: Story = {
  args: {
    size: 'md',
    children: 'Medium card content',
  },
}

export const Large: Story = {
  args: {
    size: 'lg',
    children: 'Large card content',
  },
}

export const ExtraLarge: Story = {
  args: {
    size: 'xl',
    children: 'Extra large card content',
  },
}

// Interactive states
export const Interactive: Story = {
  args: {
    interactive: true,
    children: 'Click me! I\'m an interactive card.',
    onClick: () => alert('Card clicked!'),
  },
}

export const Loading: Story = {
  args: {
    loading: true,
    children: 'This card is loading...',
  },
}

export const Disabled: Story = {
  args: {
    interactive: true,
    disabled: true,
    children: 'This card is disabled.',
  },
}

// With header and footer
export const WithHeader: Story = {
  args: {
    header: (
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Card Title</h3>
        <Star className="w-5 h-5" />
      </div>
    ),
    children: 'This card has a header with title and icon.',
  },
}

export const WithFooter: Story = {
  args: {
    footer: (
      <div className="flex justify-end">
        <Button size="sm">Action</Button>
      </div>
    ),
    children: 'This card has a footer with action button.',
  },
}

export const WithHeaderAndFooter: Story = {
  args: {
    header: (
      <div className="flex items-center space-x-3">
        <Heart className="w-6 h-6 text-red-500" />
        <h3 className="text-lg font-semibold">Complete Card</h3>
      </div>
    ),
    footer: (
      <div className="flex justify-between items-center">
        <span className="text-sm text-gray-500">Last updated: Today</span>
        <Button size="sm" variant="outline">
          View Details
        </Button>
      </div>
    ),
    children: 'This card demonstrates both header and footer sections with various content.',
  },
}

// Link behavior
export const AsLink: Story = {
  args: {
    href: '/example',
    children: 'This card acts as a link. Click to navigate.',
    variant: 'elevated',
  },
}

// Complex examples
export const ProductCard: Story = {
  args: {
    variant: 'elevated',
    interactive: true,
    header: (
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Premium Plan</h3>
        <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
          Popular
        </span>
      </div>
    ),
    footer: (
      <div className="space-y-3">
        <div className="text-2xl font-bold">$29/month</div>
        <Button fullWidth variant="gradient">
          Get Started
        </Button>
      </div>
    ),
    children: (
      <ul className="space-y-2 text-sm">
        <li>✓ Unlimited projects</li>
        <li>✓ Advanced analytics</li>
        <li>✓ Priority support</li>
        <li>✓ Custom integrations</li>
      </ul>
    ),
  },
}

export const ProfileCard: Story = {
  args: {
    variant: 'outlined',
    header: (
      <div className="flex items-center space-x-4">
        <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold">
          JD
        </div>
        <div>
          <h3 className="font-semibold">John Doe</h3>
          <p className="text-sm text-gray-500">Software Engineer</p>
        </div>
      </div>
    ),
    footer: (
      <div className="flex space-x-2">
        <Button size="sm" variant="outline" className="flex-1">
          Message
        </Button>
        <Button size="sm" variant="primary" className="flex-1">
          Connect
        </Button>
      </div>
    ),
    children: (
      <p className="text-sm">
        Passionate about creating amazing user experiences with modern web technologies.
        Always learning and sharing knowledge with the community.
      </p>
    ),
  },
}

// Showcase stories
export const AllVariants: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-4 max-w-4xl">
      <Card variant="default">Default</Card>
      <Card variant="elevated">Elevated</Card>
      <Card variant="outlined">Outlined</Card>
      <Card variant="filled">Filled</Card>
      <Card variant="glass">Glass</Card>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'All available card variants displayed together.',
      },
    },
  },
}

export const AllSizes: Story = {
  render: () => (
    <div className="space-y-4">
      <Card size="sm" variant="elevated">Small Card</Card>
      <Card size="md" variant="elevated">Medium Card</Card>
      <Card size="lg" variant="elevated">Large Card</Card>
      <Card size="xl" variant="elevated">Extra Large Card</Card>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'All available card sizes displayed together.',
      },
    },
  },
}

// Dark theme showcase
export const DarkTheme: Story = {
  decorators: [
    (Story) => (
      <ThemeProvider>
        <div className="dark bg-gray-900 p-8 rounded-lg">
          <div className="space-y-4 max-w-md">
            <h3 className="text-white text-lg font-semibold mb-4">Dark Theme Cards</h3>
            <Story />
          </div>
        </div>
      </ThemeProvider>
    ),
  ],
  render: () => (
    <div className="space-y-4">
      <Card variant="default">Default in Dark</Card>
      <Card variant="elevated">Elevated in Dark</Card>
      <Card variant="outlined">Outlined in Dark</Card>
      <Card variant="glass">Glass in Dark</Card>
    </div>
  ),
  parameters: {
    backgrounds: {
      default: 'dark',
    },
    docs: {
      description: {
        story: 'Card variants in dark theme mode.',
      },
    },
  },
}

// Interactive playground
export const Playground: Story = {
  args: {
    variant: 'elevated',
    size: 'md',
    interactive: true,
    animate: true,
    hover: true,
    children: 'Interactive playground card. Customize the props to see different variations.',
  },
  parameters: {
    docs: {
      description: {
        story: 'Interactive playground to test all card properties and combinations.',
      },
    },
  },
}
