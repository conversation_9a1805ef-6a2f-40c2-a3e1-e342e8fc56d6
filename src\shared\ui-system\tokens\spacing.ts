/**
 * ATLAS v2.4 Design Tokens - Spacing
 * Centralized spacing system for consistent layout and sizing
 */

// Base spacing scale (in rem)
export const spacing = {
  0: '0',
  px: '1px',
  0.5: '0.125rem', // 2px
  1: '0.25rem',    // 4px
  1.5: '0.375rem', // 6px
  2: '0.5rem',     // 8px
  2.5: '0.625rem', // 10px
  3: '0.75rem',    // 12px
  3.5: '0.875rem', // 14px
  4: '1rem',       // 16px
  5: '1.25rem',    // 20px
  6: '1.5rem',     // 24px
  7: '1.75rem',    // 28px
  8: '2rem',       // 32px
  9: '2.25rem',    // 36px
  10: '2.5rem',    // 40px
  11: '2.75rem',   // 44px
  12: '3rem',      // 48px
  14: '3.5rem',    // 56px
  16: '4rem',      // 64px
  20: '5rem',      // 80px
  24: '6rem',      // 96px
  28: '7rem',      // 112px
  32: '8rem',      // 128px
  36: '9rem',      // 144px
  40: '10rem',     // 160px
  44: '11rem',     // 176px
  48: '12rem',     // 192px
  52: '13rem',     // 208px
  56: '14rem',     // 224px
  60: '15rem',     // 240px
  64: '16rem',     // 256px
  72: '18rem',     // 288px
  80: '20rem',     // 320px
  96: '24rem',     // 384px
} as const

// Component-specific spacing
export const componentSpacing = {
  // Button spacing
  button: {
    padding: {
      sm: { x: spacing[3], y: spacing[1.5] },
      md: { x: spacing[4], y: spacing[2] },
      lg: { x: spacing[6], y: spacing[3] },
      xl: { x: spacing[8], y: spacing[4] },
    },
    gap: {
      sm: spacing[2],
      md: spacing[2.5],
      lg: spacing[3],
      xl: spacing[3.5],
    },
  },
  
  // Card spacing
  card: {
    padding: {
      sm: spacing[4],
      md: spacing[6],
      lg: spacing[8],
      xl: spacing[10],
    },
    gap: spacing[4],
    margin: spacing[4],
  },
  
  // Form spacing
  form: {
    fieldGap: spacing[4],
    labelGap: spacing[2],
    groupGap: spacing[6],
    sectionGap: spacing[8],
  },
  
  // Layout spacing
  layout: {
    containerPadding: {
      mobile: spacing[4],
      tablet: spacing[6],
      desktop: spacing[8],
    },
    sectionGap: {
      mobile: spacing[12],
      tablet: spacing[16],
      desktop: spacing[20],
    },
    gridGap: {
      sm: spacing[4],
      md: spacing[6],
      lg: spacing[8],
    },
  },
  
  // Navigation spacing
  navigation: {
    itemGap: spacing[6],
    padding: spacing[4],
    height: spacing[16],
  },
} as const

// Border radius scale
export const borderRadius = {
  none: '0',
  sm: '0.125rem',   // 2px
  base: '0.25rem',  // 4px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  '3xl': '1.5rem',  // 24px
  full: '9999px',
} as const

// Border width scale
export const borderWidth = {
  0: '0',
  1: '1px',
  2: '2px',
  4: '4px',
  8: '8px',
} as const

// Z-index scale
export const zIndex = {
  auto: 'auto',
  0: '0',
  10: '10',
  20: '20',
  30: '30',
  40: '40',
  50: '50',
  dropdown: '1000',
  sticky: '1020',
  fixed: '1030',
  modal: '1040',
  popover: '1050',
  tooltip: '1060',
  toast: '1070',
  overlay: '1080',
} as const

// Breakpoints for responsive design
export const breakpoints = {
  xs: '0px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const

// Container max widths
export const containerSizes = {
  xs: '20rem',     // 320px
  sm: '24rem',     // 384px
  md: '28rem',     // 448px
  lg: '32rem',     // 512px
  xl: '36rem',     // 576px
  '2xl': '42rem',  // 672px
  '3xl': '48rem',  // 768px
  '4xl': '56rem',  // 896px
  '5xl': '64rem',  // 1024px
  '6xl': '72rem',  // 1152px
  '7xl': '80rem',  // 1280px
  full: '100%',
  screen: '100vw',
} as const

// Aspect ratios
export const aspectRatios = {
  square: '1 / 1',
  video: '16 / 9',
  photo: '4 / 3',
  portrait: '3 / 4',
  wide: '21 / 9',
  ultrawide: '32 / 9',
} as const

// Animation durations
export const durations = {
  instant: '0ms',
  fast: '150ms',
  normal: '300ms',
  slow: '500ms',
  slower: '750ms',
  slowest: '1000ms',
} as const

// Animation easing functions
export const easings = {
  linear: 'linear',
  easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
  easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
  easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
  bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
} as const

// Spacing utilities
export const spacingUtils = {
  /**
   * Get spacing value by key
   */
  getSpacing: (key: keyof typeof spacing): string => {
    return spacing[key]
  },
  
  /**
   * Get component spacing
   */
  getComponentSpacing: (component: keyof typeof componentSpacing, variant?: string): any => {
    return componentSpacing[component]
  },
  
  /**
   * Convert spacing to CSS custom properties
   */
  toCSSCustomProperties: (): Record<string, string> => {
    const cssProps: Record<string, string> = {}
    
    Object.entries(spacing).forEach(([key, value]) => {
      cssProps[`--spacing-${key}`] = value
    })
    
    Object.entries(borderRadius).forEach(([key, value]) => {
      cssProps[`--radius-${key}`] = value
    })
    
    return cssProps
  },
  
  /**
   * Get responsive spacing classes
   */
  getResponsiveSpacing: (
    property: 'p' | 'm' | 'px' | 'py' | 'pt' | 'pb' | 'pl' | 'pr' | 'mx' | 'my' | 'mt' | 'mb' | 'ml' | 'mr',
    mobile: keyof typeof spacing,
    tablet?: keyof typeof spacing,
    desktop?: keyof typeof spacing
  ): string => {
    let classes = `${property}-${mobile}`
    
    if (tablet) {
      classes += ` md:${property}-${tablet}`
    }
    
    if (desktop) {
      classes += ` lg:${property}-${desktop}`
    }
    
    return classes
  },
} as const

export type SpacingKey = keyof typeof spacing
export type BorderRadiusKey = keyof typeof borderRadius
export type BorderWidthKey = keyof typeof borderWidth
export type ZIndexKey = keyof typeof zIndex
export type BreakpointKey = keyof typeof breakpoints
export type ContainerSizeKey = keyof typeof containerSizes
export type DurationKey = keyof typeof durations
export type EasingKey = keyof typeof easings
