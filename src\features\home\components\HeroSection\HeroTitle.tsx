'use client'

import React from 'react'
import { motion } from 'framer-motion'

interface HeroTitleProps {
  className?: string
}

/**
 * Hero title component
 * Main heading for the hero section with animations
 */
const HeroTitle: React.FC<HeroTitleProps> = ({ className = '' }) => {
  const animationVariants = {
    fadeInUp: {
      hidden: { opacity: 0, y: 30 },
      visible: {
        opacity: 1,
        y: 0,
        transition: { duration: 0.6, ease: [0.22, 1, 0.36, 1] },
      },
    },
  }

  return (
    <motion.div
      className={`text-center mb-6 sm:mb-8 ${className}`}
      variants={animationVariants.fadeInUp}
      initial="hidden"
      animate="visible"
    >
      <motion.h1
        className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-extrabold leading-tight text-white mb-4 sm:mb-6"
        style={{
          textShadow: '0 0 30px rgba(0, 240, 255, 0.3), 0 0 60px rgba(72, 209, 204, 0.2)',
        }}
      >
        <span className="block">Transformamos</span>
        <span className="block">
          <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#00F0FF] to-[#48D1CC]">
            Ideas
          </span>{' '}
          en{' '}
          <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#48D1CC] to-[#00B4DB]">
            Realidad
          </span>
        </span>
        <span className="block">con IA</span>
      </motion.h1>

      <motion.p
        className="text-lg sm:text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed"
        variants={animationVariants.fadeInUp}
        transition={{ delay: 0.2 }}
      >
        Especialistas en soluciones de{' '}
        <span className="text-[#00F0FF] font-semibold">Inteligencia Artificial</span> que
        impulsan el crecimiento de tu empresa con tecnología de vanguardia.
      </motion.p>
    </motion.div>
  )
}

export default HeroTitle
