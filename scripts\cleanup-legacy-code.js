#!/usr/bin/env node

/**
 * ATLAS v2.4 - Legacy Code Cleanup Script
 * Final cleanup of legacy code and optimization
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Legacy patterns to identify and clean up
const LEGACY_PATTERNS = {
  // Import patterns
  imports: [
    /import.*from ['"]@\/components\/ui\/Button['"]/g,
    /import.*from ['"]@\/components\/ui\/ButtonWithEffect['"]/g,
    /import.*ButtonWithEffect.*from/g,
    /import.*from ['"]\.\.\/ui\/Button['"]/g,
  ],
  
  // Hardcoded styles
  hardcodedStyles: [
    /className=['"][^'"]*bg-blue-\d+[^'"]*['"]/g,
    /className=['"][^'"]*text-blue-\d+[^'"]*['"]/g,
    /style=\{\{[^}]*backgroundColor:\s*['"]#[0-9a-fA-F]{6}['"][^}]*\}\}/g,
    /style=\{\{[^}]*color:\s*['"]#[0-9a-fA-F]{6}['"][^}]*\}\}/g,
  ],
  
  // Legacy theme usage
  legacyTheme: [
    /isDarkMode\s*\?\s*['"][^'"]*['"]:\s*['"][^'"]*['"]/g,
    /theme\s*===\s*['"]dark['"].*?:/g,
    /const\s+isDarkMode\s*=/g,
  ],
  
  // Unused dependencies
  unusedDeps: [
    /import.*react-icons/g,
    /import.*@heroicons/g,
    /import.*lucide-react.*(?!from ['"]lucide-react['"])/g,
  ],
  
  // Console logs and debug code
  debugCode: [
    /console\.log\([^)]*\)/g,
    /console\.warn\([^)]*\)/g,
    /console\.error\([^)]*\)/g,
    /debugger;?/g,
  ]
};

// Files and directories to clean up
const CLEANUP_TARGETS = {
  // Legacy component files to remove
  filesToRemove: [
    'src/components/ui/ButtonWithEffect.tsx',
    'src/components/ui/Button.old.tsx',
    'src/components/ui/LegacyButton.tsx',
    'src/components/ui/OldCard.tsx',
    'src/components/ui/LegacyInput.tsx',
  ],
  
  // Directories to check for empty state
  directoriesToCheck: [
    'src/components/ui',
    'src/components/legacy',
    'src/lib/legacy',
  ],
  
  // Files to analyze for cleanup
  filesToAnalyze: [
    'src/**/*.tsx',
    'src/**/*.ts',
    'src/**/*.js',
    'src/**/*.jsx',
  ]
};

/**
 * Find files matching patterns
 */
function findFiles(patterns, baseDir = '.') {
  const files = [];
  
  patterns.forEach(pattern => {
    try {
      const result = execSync(`find ${baseDir} -name "${pattern}" -type f`, { encoding: 'utf8' });
      files.push(...result.trim().split('\n').filter(f => f));
    } catch (error) {
      // Pattern not found, continue
    }
  });
  
  return [...new Set(files)]; // Remove duplicates
}

/**
 * Analyze file for legacy patterns
 */
function analyzeFile(filePath) {
  if (!fs.existsSync(filePath)) return null;
  
  const content = fs.readFileSync(filePath, 'utf8');
  const issues = [];
  
  // Check for legacy import patterns
  LEGACY_PATTERNS.imports.forEach(pattern => {
    const matches = content.match(pattern);
    if (matches) {
      issues.push({
        type: 'legacy-import',
        pattern: pattern.toString(),
        matches: matches,
        severity: 'high'
      });
    }
  });
  
  // Check for hardcoded styles
  LEGACY_PATTERNS.hardcodedStyles.forEach(pattern => {
    const matches = content.match(pattern);
    if (matches) {
      issues.push({
        type: 'hardcoded-style',
        pattern: pattern.toString(),
        matches: matches,
        severity: 'medium'
      });
    }
  });
  
  // Check for legacy theme usage
  LEGACY_PATTERNS.legacyTheme.forEach(pattern => {
    const matches = content.match(pattern);
    if (matches) {
      issues.push({
        type: 'legacy-theme',
        pattern: pattern.toString(),
        matches: matches,
        severity: 'medium'
      });
    }
  });
  
  // Check for debug code
  LEGACY_PATTERNS.debugCode.forEach(pattern => {
    const matches = content.match(pattern);
    if (matches) {
      issues.push({
        type: 'debug-code',
        pattern: pattern.toString(),
        matches: matches,
        severity: 'low'
      });
    }
  });
  
  return issues.length > 0 ? { file: filePath, issues } : null;
}

/**
 * Remove legacy files
 */
function removeLegacyFiles() {
  log('\n🗑️  Removing Legacy Files...', 'blue');
  
  let removedCount = 0;
  
  CLEANUP_TARGETS.filesToRemove.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      try {
        fs.unlinkSync(filePath);
        log(`  ✅ Removed: ${filePath}`, 'green');
        removedCount++;
      } catch (error) {
        log(`  ❌ Failed to remove: ${filePath} - ${error.message}`, 'red');
      }
    }
  });
  
  if (removedCount === 0) {
    log('  ✅ No legacy files found to remove', 'green');
  } else {
    log(`  📊 Removed ${removedCount} legacy files`, 'green');
  }
  
  return removedCount;
}

/**
 * Clean up empty directories
 */
function cleanupEmptyDirectories() {
  log('\n📁 Cleaning Empty Directories...', 'blue');
  
  let removedCount = 0;
  
  CLEANUP_TARGETS.directoriesToCheck.forEach(dirPath => {
    if (fs.existsSync(dirPath)) {
      try {
        const files = fs.readdirSync(dirPath);
        if (files.length === 0) {
          fs.rmdirSync(dirPath);
          log(`  ✅ Removed empty directory: ${dirPath}`, 'green');
          removedCount++;
        } else {
          log(`  📁 Directory not empty: ${dirPath} (${files.length} files)`, 'yellow');
        }
      } catch (error) {
        log(`  ❌ Failed to check directory: ${dirPath} - ${error.message}`, 'red');
      }
    }
  });
  
  if (removedCount === 0) {
    log('  ✅ No empty directories found', 'green');
  }
  
  return removedCount;
}

/**
 * Analyze codebase for legacy patterns
 */
function analyzeLegacyPatterns() {
  log('\n🔍 Analyzing Codebase for Legacy Patterns...', 'blue');
  
  const files = findFiles(CLEANUP_TARGETS.filesToAnalyze, 'src');
  const issues = [];
  
  files.forEach(file => {
    const fileIssues = analyzeFile(file);
    if (fileIssues) {
      issues.push(fileIssues);
    }
  });
  
  // Categorize issues by severity
  const categorized = {
    high: [],
    medium: [],
    low: []
  };
  
  issues.forEach(fileIssue => {
    fileIssue.issues.forEach(issue => {
      categorized[issue.severity].push({
        file: fileIssue.file,
        ...issue
      });
    });
  });
  
  // Display results
  if (issues.length === 0) {
    log('  ✅ No legacy patterns found!', 'green');
  } else {
    log(`  📊 Found legacy patterns in ${issues.length} files:`, 'yellow');
    
    if (categorized.high.length > 0) {
      log(`    🔴 High priority: ${categorized.high.length} issues`, 'red');
      categorized.high.slice(0, 5).forEach(issue => {
        log(`      ${issue.file}: ${issue.type}`, 'red');
      });
    }
    
    if (categorized.medium.length > 0) {
      log(`    🟡 Medium priority: ${categorized.medium.length} issues`, 'yellow');
    }
    
    if (categorized.low.length > 0) {
      log(`    🟢 Low priority: ${categorized.low.length} issues`, 'green');
    }
  }
  
  return categorized;
}

/**
 * Optimize imports and remove unused dependencies
 */
function optimizeImports() {
  log('\n📦 Optimizing Imports...', 'blue');
  
  try {
    // Run unused dependency check
    log('  🔍 Checking for unused dependencies...', 'blue');
    
    const unusedDeps = execSync('npx depcheck --json', { encoding: 'utf8' });
    const depcheckResult = JSON.parse(unusedDeps);
    
    if (depcheckResult.dependencies.length > 0) {
      log(`  📊 Found ${depcheckResult.dependencies.length} unused dependencies:`, 'yellow');
      depcheckResult.dependencies.forEach(dep => {
        log(`    - ${dep}`, 'yellow');
      });
      
      log('  💡 Run "npm uninstall <package>" to remove unused dependencies', 'blue');
    } else {
      log('  ✅ No unused dependencies found', 'green');
    }
    
    // Organize imports
    log('  🔧 Organizing imports...', 'blue');
    try {
      execSync('npx organize-imports-cli src/**/*.{ts,tsx}', { stdio: 'pipe' });
      log('  ✅ Imports organized successfully', 'green');
    } catch (error) {
      log('  ⚠️  Import organization skipped (organize-imports-cli not available)', 'yellow');
    }
    
  } catch (error) {
    log(`  ⚠️  Import optimization partially failed: ${error.message}`, 'yellow');
  }
}

/**
 * Run tree shaking analysis
 */
function analyzeTreeShaking() {
  log('\n🌳 Analyzing Tree Shaking...', 'blue');
  
  try {
    // Build and analyze bundle
    log('  🔨 Building for analysis...', 'blue');
    execSync('npm run build', { stdio: 'pipe' });
    
    // Check bundle size
    const bundleDir = '.next/static/chunks';
    if (fs.existsSync(bundleDir)) {
      const files = fs.readdirSync(bundleDir);
      const jsFiles = files.filter(f => f.endsWith('.js'));
      
      let totalSize = 0;
      jsFiles.forEach(file => {
        const filePath = path.join(bundleDir, file);
        const stats = fs.statSync(filePath);
        totalSize += stats.size;
      });
      
      const totalSizeMB = (totalSize / (1024 * 1024)).toFixed(2);
      log(`  📊 Total JS bundle size: ${totalSizeMB}MB`, 'blue');
      
      if (totalSize > 1.5 * 1024 * 1024) { // 1.5MB
        log('  ⚠️  Bundle size exceeds 1.5MB target', 'yellow');
      } else {
        log('  ✅ Bundle size within target', 'green');
      }
    }
    
  } catch (error) {
    log(`  ⚠️  Tree shaking analysis failed: ${error.message}`, 'yellow');
  }
}

/**
 * Generate cleanup report
 */
function generateCleanupReport(results) {
  const report = {
    timestamp: new Date().toISOString(),
    atlas_version: '2.4',
    cleanup_summary: {
      files_removed: results.filesRemoved,
      directories_cleaned: results.directoriesCleaned,
      legacy_patterns: results.legacyPatterns,
      total_issues: Object.values(results.legacyPatterns).reduce((sum, arr) => sum + arr.length, 0)
    },
    recommendations: []
  };
  
  // Add recommendations
  if (results.legacyPatterns.high.length > 0) {
    report.recommendations.push('Address high-priority legacy patterns immediately');
  }
  
  if (results.legacyPatterns.medium.length > 0) {
    report.recommendations.push('Plan migration for medium-priority legacy patterns');
  }
  
  if (results.legacyPatterns.low.length > 0) {
    report.recommendations.push('Consider cleanup of low-priority issues in next iteration');
  }
  
  // Save report
  const reportPath = 'reports/legacy-cleanup-report.json';
  fs.mkdirSync(path.dirname(reportPath), { recursive: true });
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  return report;
}

/**
 * Main cleanup function
 */
function cleanupLegacyCode() {
  log('🧹 ATLAS v2.4 Legacy Code Cleanup', 'cyan');
  log('==================================', 'cyan');
  
  const results = {
    filesRemoved: 0,
    directoriesCleaned: 0,
    legacyPatterns: { high: [], medium: [], low: [] }
  };
  
  // Remove legacy files
  results.filesRemoved = removeLegacyFiles();
  
  // Clean empty directories
  results.directoriesCleaned = cleanupEmptyDirectories();
  
  // Analyze legacy patterns
  results.legacyPatterns = analyzeLegacyPatterns();
  
  // Optimize imports
  optimizeImports();
  
  // Analyze tree shaking
  analyzeTreeShaking();
  
  // Generate report
  const report = generateCleanupReport(results);
  
  // Summary
  log('\n📋 Cleanup Summary:', 'bold');
  log(`  Files removed: ${results.filesRemoved}`, 'blue');
  log(`  Directories cleaned: ${results.directoriesCleaned}`, 'blue');
  
  const totalIssues = Object.values(results.legacyPatterns).reduce((sum, arr) => sum + arr.length, 0);
  if (totalIssues === 0) {
    log('  ✅ No legacy patterns found - codebase is clean!', 'green');
  } else {
    log(`  Legacy patterns found: ${totalIssues}`, 'yellow');
    log(`    High priority: ${results.legacyPatterns.high.length}`, 'red');
    log(`    Medium priority: ${results.legacyPatterns.medium.length}`, 'yellow');
    log(`    Low priority: ${results.legacyPatterns.low.length}`, 'green');
  }
  
  log(`\n📄 Detailed report saved to: reports/legacy-cleanup-report.json`, 'blue');
  
  return totalIssues === 0 && results.legacyPatterns.high.length === 0;
}

// Run if called directly
if (require.main === module) {
  try {
    const success = cleanupLegacyCode();
    
    if (success) {
      log('\n✅ Legacy code cleanup completed successfully!', 'green');
      process.exit(0);
    } else {
      log('\n⚠️  Legacy code cleanup completed with issues to address', 'yellow');
      process.exit(1);
    }
  } catch (error) {
    log(`💥 Legacy code cleanup failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

module.exports = { cleanupLegacyCode };
