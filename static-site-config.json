{"routes": [{"src": "/(.*)", "dest": "/$1"}, {"src": "/", "dest": "/index.html"}, {"src": "/about", "dest": "/about/index.html"}, {"src": "/services", "dest": "/services/index.html"}, {"src": "/contact", "dest": "/contact/index.html"}, {"src": "/blog", "dest": "/blog/index.html"}, {"src": "/success-cases", "dest": "/success-cases/index.html"}, {"src": "/resources", "dest": "/resources/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=3600, must-revalidate"}]}, {"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}