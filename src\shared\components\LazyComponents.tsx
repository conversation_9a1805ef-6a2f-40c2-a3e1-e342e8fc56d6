/**
 * ATLAS v2.4 - Lazy Loading Components
 * Implements code splitting for better performance
 */

import { lazy, Suspense } from 'react'
import { motion } from 'framer-motion'

// Loading component
const LoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-[200px]">
    <motion.div
      className="w-8 h-8 border-2 border-[#00B4DB] border-t-transparent rounded-full"
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
    />
  </div>
)

// Lazy loaded components
export const LazyContactPage = lazy(() => import('@/features/contact/ContactPage'))
export const LazyAboutPage = lazy(() => import('@/features/about/AboutPage'))
export const LazyOrgChart = lazy(() => import('@/components/about/OrgChart'))
export const LazyNeuralNetworkBackground = lazy(() => import('@/components/ui/NeuralNetworkBackground'))

// HOC for lazy loading with suspense
export const withLazyLoading = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ReactNode
) => {
  return (props: P) => (
    <Suspense fallback={fallback || <LoadingSpinner />}>
      <Component {...props} />
    </Suspense>
  )
}

// Pre-configured lazy components with suspense
export const ContactPageWithSuspense = withLazyLoading(LazyContactPage)
export const AboutPageWithSuspense = withLazyLoading(LazyAboutPage)
export const OrgChartWithSuspense = withLazyLoading(LazyOrgChart)
export const NeuralNetworkBackgroundWithSuspense = withLazyLoading(LazyNeuralNetworkBackground)
