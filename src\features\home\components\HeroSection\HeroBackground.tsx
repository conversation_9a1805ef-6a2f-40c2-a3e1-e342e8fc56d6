'use client'

import React from 'react'
import NeuralNetworkBackground from '@/components/ui/NeuralNetworkBackground'

interface HeroBackgroundProps {
  children: React.ReactNode
  className?: string
}

/**
 * Hero background component with neural network and decorative elements
 * Extracted from HeroSection for better modularity
 */
const HeroBackground: React.FC<HeroBackgroundProps> = ({
  children,
  className = '',
}) => {
  return (
    <section className={`relative py-24 sm:py-28 md:py-36 lg:py-44 overflow-hidden bg-gray-900 code-lines-bg ${className}`}>
      {/* Glow effects */}
      <div className="absolute top-0 left-1/4 w-1/2 h-1/3 rounded-full filter blur-[120px] bg-[#00B4DB]/20" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full filter blur-[150px] bg-[#48D1CC]/15" />

      {/* Neural Network Background */}
      <NeuralNetworkBackground />

      {/* Grid pattern overlay */}
      <div className="absolute inset-0 bg-grid-white/[0.03] bg-[length:30px_30px]" />

      {/* Decorative dots pattern */}
      <div className="absolute -right-8 bottom-32 w-24 h-48 opacity-40">
        <div className="w-2 h-2 rounded-full bg-[#00BFFF]/60 absolute top-0 left-0" />
        <div className="w-2 h-2 rounded-full bg-[#48D1CC]/60 absolute top-8 left-8" />
        <div className="w-2 h-2 rounded-full bg-[#00B4DB]/60 absolute top-16 left-0" />
        <div className="w-2 h-2 rounded-full bg-[#48D1CC]/60 absolute top-24 left-8" />
      </div>

      {/* Content */}
      <div className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8">
        {children}
      </div>
    </section>
  )
}

export default HeroBackground
