'use client'

import React from 'react'
import { useThemeStyles } from '@/shared/ui-system'

interface FormFieldOption {
  value: string
  label: string
}

interface FormFieldProps {
  label: string
  name: string
  type: 'text' | 'email' | 'tel' | 'textarea' | 'select'
  value: string
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void
  required?: boolean
  placeholder?: string
  rows?: number
  options?: FormFieldOption[]
  className?: string
}

/**
 * Form Field Component
 * Reusable form field with consistent styling and theme support
 * Extracted from ContactForm for better modularity
 */
const FormField: React.FC<FormFieldProps> = ({
  label,
  name,
  type,
  value,
  onChange,
  required = false,
  placeholder,
  rows = 3,
  options = [],
  className = '',
}) => {
  const themeStyles = useThemeStyles()
  
  // Base input styles
  const baseInputStyles = `
    w-full px-4 py-3 rounded-lg border-2 transition-all duration-300
    focus:outline-none focus:ring-2 focus:ring-offset-2
    ${themeStyles.background.secondary}
    ${themeStyles.text.primary}
    ${themeStyles.border.primary}
    focus:${themeStyles.border.focus}
    placeholder:${themeStyles.text.muted}
  `
  
  // Render input based on type
  const renderInput = () => {
    switch (type) {
      case 'textarea':
        return (
          <textarea
            id={name}
            name={name}
            value={value}
            onChange={onChange}
            required={required}
            placeholder={placeholder}
            rows={rows}
            className={`${baseInputStyles} resize-none`}
          />
        )
      
      case 'select':
        return (
          <select
            id={name}
            name={name}
            value={value}
            onChange={onChange}
            required={required}
            className={baseInputStyles}
          >
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        )
      
      default:
        return (
          <input
            type={type}
            id={name}
            name={name}
            value={value}
            onChange={onChange}
            required={required}
            placeholder={placeholder}
            className={baseInputStyles}
          />
        )
    }
  }
  
  return (
    <div className={`space-y-2 ${className}`}>
      <label
        htmlFor={name}
        className={`block text-sm font-medium ${themeStyles.text.primary}`}
      >
        {label}
        {required && (
          <span className="text-red-500 ml-1" aria-label="required">
            *
          </span>
        )}
      </label>
      
      {renderInput()}
    </div>
  )
}

export default FormField
