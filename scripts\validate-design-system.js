#!/usr/bin/env node

/**
 * ATLAS v2.4 Design System Validation Script
 * Validates the unified design system implementation
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function validateDesignSystem() {
  log('\n🎨 ATLAS v2.4 Design System Validation', 'blue');
  log('=========================================', 'blue');
  
  let validationErrors = [];
  let validationWarnings = [];
  
  // Check design tokens structure
  log('\n📋 Validating Design Tokens...', 'bold');
  
  const tokenFiles = [
    'src/shared/ui-system/tokens/colors.ts',
    'src/shared/ui-system/tokens/typography.ts',
    'src/shared/ui-system/tokens/spacing.ts',
    'src/shared/ui-system/tokens/animations.ts'
  ];
  
  tokenFiles.forEach(file => {
    if (fs.existsSync(file)) {
      log(`  ✅ ${file}`, 'green');
    } else {
      log(`  ❌ ${file} - Missing`, 'red');
      validationErrors.push(`Missing design token file: ${file}`);
    }
  });
  
  // Check hooks
  log('\n🪝 Validating Design System Hooks...', 'bold');
  
  const hookFiles = [
    'src/shared/ui-system/hooks/useDesignTokens.ts',
    'src/shared/ui-system/hooks/useThemeStyles.ts'
  ];
  
  hookFiles.forEach(file => {
    if (fs.existsSync(file)) {
      log(`  ✅ ${file}`, 'green');
    } else {
      log(`  ❌ ${file} - Missing`, 'red');
      validationErrors.push(`Missing hook file: ${file}`);
    }
  });
  
  // Check unified Button component
  log('\n🔘 Validating Unified Button Component...', 'bold');
  
  const buttonFiles = [
    'src/shared/ui-system/components/Button/Button.tsx',
    'src/shared/ui-system/components/Button/Button.stories.tsx',
    'src/shared/ui-system/components/Button/__tests__/Button.test.tsx',
    'src/shared/ui-system/components/Button/index.ts'
  ];
  
  buttonFiles.forEach(file => {
    if (fs.existsSync(file)) {
      log(`  ✅ ${file}`, 'green');
    } else {
      log(`  ❌ ${file} - Missing`, 'red');
      validationErrors.push(`Missing button file: ${file}`);
    }
  });
  
  // Check for old button components (should be deprecated)
  log('\n🗑️  Checking for Legacy Components...', 'bold');
  
  const legacyFiles = [
    'src/components/ui/Button.tsx',
    'src/components/ui/ButtonWithEffect.tsx'
  ];
  
  legacyFiles.forEach(file => {
    if (fs.existsSync(file)) {
      log(`  ⚠️  ${file} - Legacy component still exists`, 'yellow');
      validationWarnings.push(`Legacy component should be migrated: ${file}`);
    } else {
      log(`  ✅ ${file} - Properly removed/migrated`, 'green');
    }
  });
  
  // Check utilities
  log('\n🛠️  Validating Utilities...', 'bold');
  
  const utilFiles = [
    'src/utils/cn.ts'
  ];
  
  utilFiles.forEach(file => {
    if (fs.existsSync(file)) {
      log(`  ✅ ${file}`, 'green');
    } else {
      log(`  ❌ ${file} - Missing`, 'red');
      validationErrors.push(`Missing utility file: ${file}`);
    }
  });
  
  // Check main export
  log('\n📦 Validating Main Export...', 'bold');
  
  const mainExport = 'src/shared/ui-system/index.ts';
  if (fs.existsSync(mainExport)) {
    log(`  ✅ ${mainExport}`, 'green');
  } else {
    log(`  ❌ ${mainExport} - Missing`, 'red');
    validationErrors.push(`Missing main export file: ${mainExport}`);
  }
  
  // Check package.json dependencies
  log('\n📋 Validating Dependencies...', 'bold');
  
  const packageJsonPath = 'package.json';
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const requiredDeps = ['clsx', 'tailwind-merge', 'framer-motion', 'lucide-react'];
    
    requiredDeps.forEach(dep => {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        log(`  ✅ ${dep} - ${packageJson.dependencies[dep]}`, 'green');
      } else {
        log(`  ❌ ${dep} - Missing dependency`, 'red');
        validationErrors.push(`Missing required dependency: ${dep}`);
      }
    });
  }
  
  // Validate component structure
  log('\n🏗️  Validating Component Structure...', 'bold');
  
  const buttonComponentPath = 'src/shared/ui-system/components/Button/Button.tsx';
  if (fs.existsSync(buttonComponentPath)) {
    const buttonContent = fs.readFileSync(buttonComponentPath, 'utf8');
    
    // Check for key features
    const features = [
      { name: 'forwardRef', pattern: /forwardRef/, required: true },
      { name: 'ButtonProps interface', pattern: /interface ButtonProps/, required: true },
      { name: 'Variant support', pattern: /variant.*primary.*secondary/, required: true },
      { name: 'Effect support', pattern: /effect.*ripple.*particles/, required: true },
      { name: 'Loading state', pattern: /loading/, required: true },
      { name: 'Icon support', pattern: /icon.*iconPosition/, required: true },
      { name: 'Link functionality', pattern: /href/, required: true },
      { name: 'Accessibility', pattern: /aria-label.*aria-disabled/, required: true }
    ];
    
    features.forEach(feature => {
      if (feature.pattern.test(buttonContent)) {
        log(`  ✅ ${feature.name}`, 'green');
      } else if (feature.required) {
        log(`  ❌ ${feature.name} - Missing`, 'red');
        validationErrors.push(`Button component missing feature: ${feature.name}`);
      } else {
        log(`  ⚠️  ${feature.name} - Optional feature missing`, 'yellow');
        validationWarnings.push(`Button component optional feature missing: ${feature.name}`);
      }
    });
  }
  
  // Check for component size compliance
  log('\n📏 Validating Component Size (ATLAS <200 lines)...', 'bold');
  
  const componentsToCheck = [
    'src/shared/ui-system/components/Button/Button.tsx',
    'src/shared/ui-system/hooks/useDesignTokens.ts',
    'src/shared/ui-system/hooks/useThemeStyles.ts'
  ];
  
  componentsToCheck.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      const lines = content.split('\n').length;
      
      if (lines <= 200) {
        log(`  ✅ ${file} - ${lines} lines (within ATLAS limit)`, 'green');
      } else if (lines <= 300) {
        log(`  ⚠️  ${file} - ${lines} lines (exceeds ATLAS limit but acceptable)`, 'yellow');
        validationWarnings.push(`Component exceeds 200 lines: ${file} (${lines} lines)`);
      } else {
        log(`  ❌ ${file} - ${lines} lines (significantly exceeds ATLAS limit)`, 'red');
        validationErrors.push(`Component significantly exceeds 200 lines: ${file} (${lines} lines)`);
      }
    }
  });
  
  // Summary
  log('\n📊 Validation Summary:', 'bold');
  log('===================', 'blue');
  
  if (validationErrors.length === 0 && validationWarnings.length === 0) {
    log('🎉 All validations passed! Design system is properly implemented.', 'green');
  } else {
    if (validationErrors.length > 0) {
      log(`❌ ${validationErrors.length} error(s) found:`, 'red');
      validationErrors.forEach(error => log(`   • ${error}`, 'red'));
    }
    
    if (validationWarnings.length > 0) {
      log(`⚠️  ${validationWarnings.length} warning(s) found:`, 'yellow');
      validationWarnings.forEach(warning => log(`   • ${warning}`, 'yellow'));
    }
  }
  
  // Recommendations
  if (validationWarnings.length > 0 || validationErrors.length > 0) {
    log('\n💡 Recommendations:', 'blue');
    
    if (validationErrors.length > 0) {
      log('1. Fix all errors before proceeding to production', 'blue');
      log('2. Run tests to ensure functionality is preserved', 'blue');
    }
    
    if (validationWarnings.length > 0) {
      log('3. Consider addressing warnings for optimal design system', 'blue');
      log('4. Update migration plan for legacy components', 'blue');
    }
  }
  
  // Exit with appropriate code
  if (validationErrors.length > 0) {
    log('\n❌ Validation failed due to errors.', 'red');
    process.exit(1);
  } else {
    log('\n✅ Validation completed successfully!', 'green');
    process.exit(0);
  }
}

// Run validation
try {
  validateDesignSystem();
} catch (error) {
  log(`❌ Error during validation: ${error.message}`, 'red');
  process.exit(1);
}
