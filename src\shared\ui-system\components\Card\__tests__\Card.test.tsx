import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ThemeProvider } from '@/context/ThemeContext'
import Card from '../Card'

// Test wrapper with theme context
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider>{children}</ThemeProvider>
)

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}))

describe('Card Component - ATLAS v2.4', () => {
  describe('Basic Rendering', () => {
    it('renders with default props', () => {
      render(
        <TestWrapper>
          <Card>Card content</Card>
        </TestWrapper>
      )
      
      expect(screen.getByText('Card content')).toBeInTheDocument()
    })

    it('renders with custom className', () => {
      render(
        <TestWrapper>
          <Card className="custom-class">Content</Card>
        </TestWrapper>
      )
      
      const card = screen.getByText('Content').parentElement
      expect(card).toHaveClass('custom-class')
    })
  })

  describe('Variants', () => {
    const variants = ['default', 'elevated', 'outlined', 'filled', 'glass'] as const

    variants.forEach(variant => {
      it(`renders ${variant} variant correctly`, () => {
        render(
          <TestWrapper>
            <Card variant={variant}>{variant} Card</Card>
          </TestWrapper>
        )
        
        expect(screen.getByText(`${variant} Card`)).toBeInTheDocument()
      })
    })
  })

  describe('Sizes', () => {
    const sizes = ['sm', 'md', 'lg', 'xl'] as const

    sizes.forEach(size => {
      it(`renders ${size} size correctly`, () => {
        render(
          <TestWrapper>
            <Card size={size}>{size} Card</Card>
          </TestWrapper>
        )
        
        expect(screen.getByText(`${size} Card`)).toBeInTheDocument()
      })
    })
  })

  describe('Header and Footer', () => {
    it('renders with header', () => {
      render(
        <TestWrapper>
          <Card header={<h3>Card Header</h3>}>
            Card content
          </Card>
        </TestWrapper>
      )
      
      expect(screen.getByText('Card Header')).toBeInTheDocument()
      expect(screen.getByText('Card content')).toBeInTheDocument()
    })

    it('renders with footer', () => {
      render(
        <TestWrapper>
          <Card footer={<p>Card Footer</p>}>
            Card content
          </Card>
        </TestWrapper>
      )
      
      expect(screen.getByText('Card Footer')).toBeInTheDocument()
      expect(screen.getByText('Card content')).toBeInTheDocument()
    })

    it('renders with both header and footer', () => {
      render(
        <TestWrapper>
          <Card 
            header={<h3>Header</h3>}
            footer={<p>Footer</p>}
          >
            Content
          </Card>
        </TestWrapper>
      )
      
      expect(screen.getByText('Header')).toBeInTheDocument()
      expect(screen.getByText('Content')).toBeInTheDocument()
      expect(screen.getByText('Footer')).toBeInTheDocument()
    })
  })

  describe('Interactive States', () => {
    it('handles click events when interactive', async () => {
      const handleClick = jest.fn()
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Card interactive onClick={handleClick}>
            Clickable Card
          </Card>
        </TestWrapper>
      )
      
      const card = screen.getByText('Clickable Card').parentElement
      await user.click(card!)
      
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it('does not trigger click when disabled', async () => {
      const handleClick = jest.fn()
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Card interactive disabled onClick={handleClick}>
            Disabled Card
          </Card>
        </TestWrapper>
      )
      
      const card = screen.getByText('Disabled Card').parentElement
      await user.click(card!)
      
      expect(handleClick).not.toHaveBeenCalled()
    })

    it('shows loading state', () => {
      render(
        <TestWrapper>
          <Card loading>Loading Card</Card>
        </TestWrapper>
      )
      
      // Check for loading spinner
      const spinner = document.querySelector('.border-t-transparent')
      expect(spinner).toBeInTheDocument()
    })
  })

  describe('Link Behavior', () => {
    it('renders as link when href is provided', () => {
      render(
        <TestWrapper>
          <Card href="/test">Link Card</Card>
        </TestWrapper>
      )
      
      const link = screen.getByRole('link')
      expect(link).toHaveAttribute('href', '/test')
    })

    it('does not render as link when disabled', () => {
      render(
        <TestWrapper>
          <Card href="/test" disabled>Disabled Link Card</Card>
        </TestWrapper>
      )
      
      expect(screen.queryByRole('link')).not.toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA attributes when interactive', () => {
      render(
        <TestWrapper>
          <Card interactive aria-label="Interactive card">
            Card content
          </Card>
        </TestWrapper>
      )
      
      const card = screen.getByRole('button')
      expect(card).toHaveAttribute('aria-label', 'Interactive card')
      expect(card).toHaveAttribute('tabIndex', '0')
    })

    it('sets aria-disabled when disabled', () => {
      render(
        <TestWrapper>
          <Card interactive disabled>
            Disabled card
          </Card>
        </TestWrapper>
      )
      
      const card = screen.getByText('Disabled card').parentElement
      expect(card).toHaveAttribute('aria-disabled', 'true')
    })

    it('supports keyboard navigation', async () => {
      const handleClick = jest.fn()
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Card interactive onClick={handleClick}>
            Keyboard Card
          </Card>
        </TestWrapper>
      )
      
      const card = screen.getByRole('button')
      card.focus()
      
      await user.keyboard('{Enter}')
      expect(handleClick).toHaveBeenCalledTimes(1)
    })
  })

  describe('Styling Props', () => {
    it('applies custom padding', () => {
      render(
        <TestWrapper>
          <Card padding="lg">Padded Card</Card>
        </TestWrapper>
      )
      
      const card = screen.getByText('Padded Card').parentElement
      expect(card).toHaveClass('p-6')
    })

    it('applies custom radius', () => {
      render(
        <TestWrapper>
          <Card radius="xl">Rounded Card</Card>
        </TestWrapper>
      )
      
      const card = screen.getByText('Rounded Card').parentElement
      expect(card).toHaveClass('rounded-2xl')
    })
  })

  describe('Theme Integration', () => {
    it('adapts to theme changes', () => {
      render(
        <TestWrapper>
          <Card>Themed Card</Card>
        </TestWrapper>
      )
      
      expect(screen.getByText('Themed Card')).toBeInTheDocument()
    })
  })

  describe('Performance', () => {
    it('renders efficiently without unnecessary re-renders', () => {
      const { rerender } = render(
        <TestWrapper>
          <Card>Card content</Card>
        </TestWrapper>
      )
      
      rerender(
        <TestWrapper>
          <Card>Card content</Card>
        </TestWrapper>
      )
      
      expect(screen.getByText('Card content')).toBeInTheDocument()
    })
  })
})
