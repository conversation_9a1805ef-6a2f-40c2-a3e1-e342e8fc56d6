/**
 * ATLAS v2.4 Design Tokens - Typography
 * Centralized typography system for consistent text styling
 */

// Font families
export const fontFamilies = {
  sans: [
    'Inter',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Oxygen',
    'Ubuntu',
    'Cantarell',
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    'sans-serif',
  ],
  mono: [
    'JetBrains Mono',
    'Fira Code',
    'Monaco',
    'Consolas',
    'Liberation Mono',
    'Courier New',
    'monospace',
  ],
  display: [
    'Cal Sans',
    'Inter',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'sans-serif',
  ],
} as const

// Font weights
export const fontWeights = {
  thin: 100,
  extralight: 200,
  light: 300,
  normal: 400,
  medium: 500,
  semibold: 600,
  bold: 700,
  extrabold: 800,
  black: 900,
} as const

// Font sizes with line heights
export const fontSizes = {
  xs: {
    fontSize: '0.75rem', // 12px
    lineHeight: '1rem', // 16px
  },
  sm: {
    fontSize: '0.875rem', // 14px
    lineHeight: '1.25rem', // 20px
  },
  base: {
    fontSize: '1rem', // 16px
    lineHeight: '1.5rem', // 24px
  },
  lg: {
    fontSize: '1.125rem', // 18px
    lineHeight: '1.75rem', // 28px
  },
  xl: {
    fontSize: '1.25rem', // 20px
    lineHeight: '1.75rem', // 28px
  },
  '2xl': {
    fontSize: '1.5rem', // 24px
    lineHeight: '2rem', // 32px
  },
  '3xl': {
    fontSize: '1.875rem', // 30px
    lineHeight: '2.25rem', // 36px
  },
  '4xl': {
    fontSize: '2.25rem', // 36px
    lineHeight: '2.5rem', // 40px
  },
  '5xl': {
    fontSize: '3rem', // 48px
    lineHeight: '1',
  },
  '6xl': {
    fontSize: '3.75rem', // 60px
    lineHeight: '1',
  },
  '7xl': {
    fontSize: '4.5rem', // 72px
    lineHeight: '1',
  },
  '8xl': {
    fontSize: '6rem', // 96px
    lineHeight: '1',
  },
  '9xl': {
    fontSize: '8rem', // 128px
    lineHeight: '1',
  },
} as const

// Letter spacing
export const letterSpacing = {
  tighter: '-0.05em',
  tight: '-0.025em',
  normal: '0em',
  wide: '0.025em',
  wider: '0.05em',
  widest: '0.1em',
} as const

// Text styles for common use cases
export const textStyles = {
  // Headings
  h1: {
    fontFamily: fontFamilies.display.join(', '),
    fontSize: fontSizes['5xl'].fontSize,
    lineHeight: fontSizes['5xl'].lineHeight,
    fontWeight: fontWeights.bold,
    letterSpacing: letterSpacing.tight,
  },
  h2: {
    fontFamily: fontFamilies.display.join(', '),
    fontSize: fontSizes['4xl'].fontSize,
    lineHeight: fontSizes['4xl'].lineHeight,
    fontWeight: fontWeights.bold,
    letterSpacing: letterSpacing.tight,
  },
  h3: {
    fontFamily: fontFamilies.display.join(', '),
    fontSize: fontSizes['3xl'].fontSize,
    lineHeight: fontSizes['3xl'].lineHeight,
    fontWeight: fontWeights.semibold,
    letterSpacing: letterSpacing.tight,
  },
  h4: {
    fontFamily: fontFamilies.sans.join(', '),
    fontSize: fontSizes['2xl'].fontSize,
    lineHeight: fontSizes['2xl'].lineHeight,
    fontWeight: fontWeights.semibold,
    letterSpacing: letterSpacing.normal,
  },
  h5: {
    fontFamily: fontFamilies.sans.join(', '),
    fontSize: fontSizes.xl.fontSize,
    lineHeight: fontSizes.xl.lineHeight,
    fontWeight: fontWeights.semibold,
    letterSpacing: letterSpacing.normal,
  },
  h6: {
    fontFamily: fontFamilies.sans.join(', '),
    fontSize: fontSizes.lg.fontSize,
    lineHeight: fontSizes.lg.lineHeight,
    fontWeight: fontWeights.semibold,
    letterSpacing: letterSpacing.normal,
  },
  
  // Body text
  body: {
    fontFamily: fontFamilies.sans.join(', '),
    fontSize: fontSizes.base.fontSize,
    lineHeight: fontSizes.base.lineHeight,
    fontWeight: fontWeights.normal,
    letterSpacing: letterSpacing.normal,
  },
  bodyLarge: {
    fontFamily: fontFamilies.sans.join(', '),
    fontSize: fontSizes.lg.fontSize,
    lineHeight: fontSizes.lg.lineHeight,
    fontWeight: fontWeights.normal,
    letterSpacing: letterSpacing.normal,
  },
  bodySmall: {
    fontFamily: fontFamilies.sans.join(', '),
    fontSize: fontSizes.sm.fontSize,
    lineHeight: fontSizes.sm.lineHeight,
    fontWeight: fontWeights.normal,
    letterSpacing: letterSpacing.normal,
  },
  
  // UI text
  button: {
    fontFamily: fontFamilies.sans.join(', '),
    fontSize: fontSizes.base.fontSize,
    lineHeight: fontSizes.base.lineHeight,
    fontWeight: fontWeights.medium,
    letterSpacing: letterSpacing.normal,
  },
  buttonSmall: {
    fontFamily: fontFamilies.sans.join(', '),
    fontSize: fontSizes.sm.fontSize,
    lineHeight: fontSizes.sm.lineHeight,
    fontWeight: fontWeights.medium,
    letterSpacing: letterSpacing.normal,
  },
  buttonLarge: {
    fontFamily: fontFamilies.sans.join(', '),
    fontSize: fontSizes.lg.fontSize,
    lineHeight: fontSizes.lg.lineHeight,
    fontWeight: fontWeights.medium,
    letterSpacing: letterSpacing.normal,
  },
  
  // Labels and captions
  label: {
    fontFamily: fontFamilies.sans.join(', '),
    fontSize: fontSizes.sm.fontSize,
    lineHeight: fontSizes.sm.lineHeight,
    fontWeight: fontWeights.medium,
    letterSpacing: letterSpacing.normal,
  },
  caption: {
    fontFamily: fontFamilies.sans.join(', '),
    fontSize: fontSizes.xs.fontSize,
    lineHeight: fontSizes.xs.lineHeight,
    fontWeight: fontWeights.normal,
    letterSpacing: letterSpacing.normal,
  },
  
  // Code
  code: {
    fontFamily: fontFamilies.mono.join(', '),
    fontSize: fontSizes.sm.fontSize,
    lineHeight: fontSizes.sm.lineHeight,
    fontWeight: fontWeights.normal,
    letterSpacing: letterSpacing.normal,
  },
  
  // Special styles
  hero: {
    fontFamily: fontFamilies.display.join(', '),
    fontSize: fontSizes['7xl'].fontSize,
    lineHeight: fontSizes['7xl'].lineHeight,
    fontWeight: fontWeights.extrabold,
    letterSpacing: letterSpacing.tight,
  },
  subtitle: {
    fontFamily: fontFamilies.sans.join(', '),
    fontSize: fontSizes['2xl'].fontSize,
    lineHeight: fontSizes['2xl'].lineHeight,
    fontWeight: fontWeights.normal,
    letterSpacing: letterSpacing.normal,
  },
} as const

// Responsive typography scales
export const responsiveTextStyles = {
  hero: {
    mobile: {
      fontSize: fontSizes['4xl'].fontSize,
      lineHeight: fontSizes['4xl'].lineHeight,
    },
    tablet: {
      fontSize: fontSizes['6xl'].fontSize,
      lineHeight: fontSizes['6xl'].lineHeight,
    },
    desktop: {
      fontSize: fontSizes['7xl'].fontSize,
      lineHeight: fontSizes['7xl'].lineHeight,
    },
  },
  h1: {
    mobile: {
      fontSize: fontSizes['3xl'].fontSize,
      lineHeight: fontSizes['3xl'].lineHeight,
    },
    tablet: {
      fontSize: fontSizes['4xl'].fontSize,
      lineHeight: fontSizes['4xl'].lineHeight,
    },
    desktop: {
      fontSize: fontSizes['5xl'].fontSize,
      lineHeight: fontSizes['5xl'].lineHeight,
    },
  },
  h2: {
    mobile: {
      fontSize: fontSizes['2xl'].fontSize,
      lineHeight: fontSizes['2xl'].lineHeight,
    },
    tablet: {
      fontSize: fontSizes['3xl'].fontSize,
      lineHeight: fontSizes['3xl'].lineHeight,
    },
    desktop: {
      fontSize: fontSizes['4xl'].fontSize,
      lineHeight: fontSizes['4xl'].lineHeight,
    },
  },
} as const

// Typography utilities
export const typographyUtils = {
  /**
   * Get responsive font size classes
   */
  getResponsiveFontSize: (style: keyof typeof responsiveTextStyles): string => {
    const responsive = responsiveTextStyles[style]
    return `text-[${responsive.mobile.fontSize}] md:text-[${responsive.tablet.fontSize}] lg:text-[${responsive.desktop.fontSize}]`
  },
  
  /**
   * Get text style as CSS properties
   */
  getTextStyleCSS: (style: keyof typeof textStyles): React.CSSProperties => {
    return textStyles[style] as React.CSSProperties
  },
  
  /**
   * Get font family string
   */
  getFontFamily: (family: keyof typeof fontFamilies): string => {
    return fontFamilies[family].join(', ')
  },
} as const

export type FontFamily = keyof typeof fontFamilies
export type FontWeight = keyof typeof fontWeights
export type FontSize = keyof typeof fontSizes
export type TextStyle = keyof typeof textStyles
export type LetterSpacing = keyof typeof letterSpacing
