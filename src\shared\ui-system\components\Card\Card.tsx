'use client'

import React, { forwardRef } from 'react'
import { motion, MotionProps } from 'framer-motion'
import { cn } from '@/utils/cn'
import { useThemeStyles } from '../../hooks/useThemeStyles'

// Types
export interface CardProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'children'> {
  // Appearance
  variant?: 'default' | 'elevated' | 'outlined' | 'filled' | 'glass'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  
  // Content
  children: React.ReactNode
  header?: React.ReactNode
  footer?: React.ReactNode
  
  // Behavior
  interactive?: boolean
  loading?: boolean
  disabled?: boolean
  
  // Animation
  animate?: boolean
  hover?: boolean
  
  // Styling
  className?: string
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  radius?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full'
  
  // Link behavior
  href?: string
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void
  
  // Accessibility
  'aria-label'?: string
  role?: string
}

/**
 * ATLAS v2.4 Card Component
 * 
 * Versatile card component with multiple variants, animations, and full accessibility
 * 
 * Features:
 * - 5 visual variants (default, elevated, outlined, filled, glass)
 * - 4 sizes with design tokens
 * - Interactive states and animations
 * - Header and footer support
 * - Loading and disabled states
 * - Full accessibility support
 * - Theme-aware styling
 * 
 * @param props - Card properties
 * @returns Card component
 */
const Card = forwardRef<HTMLDivElement, CardProps & MotionProps>(({
  // Appearance
  variant = 'default',
  size = 'md',
  
  // Content
  children,
  header,
  footer,
  
  // Behavior
  interactive = false,
  loading = false,
  disabled = false,
  
  // Animation
  animate = true,
  hover = true,
  
  // Styling
  className = '',
  padding = 'md',
  radius = 'md',
  
  // Link behavior
  href,
  onClick,
  
  // Accessibility
  'aria-label': ariaLabel,
  role,
  
  // Motion props
  ...motionProps
}, ref) => {
  const themeStyles = useThemeStyles()
  
  // Variant styles
  const variantStyles = {
    default: `${themeStyles.background.secondary} ${themeStyles.border.primary} border`,
    elevated: `${themeStyles.background.secondary} shadow-lg hover:shadow-xl transition-shadow duration-300`,
    outlined: `${themeStyles.background.secondary} ${themeStyles.border.secondary} border-2`,
    filled: `${themeStyles.background.primary}`,
    glass: `backdrop-blur-md bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10`,
  }
  
  // Size styles
  const sizeStyles = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
  }
  
  // Padding styles
  const paddingStyles = {
    none: 'p-0',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-8',
  }
  
  // Radius styles
  const radiusStyles = {
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-lg',
    lg: 'rounded-xl',
    xl: 'rounded-2xl',
    full: 'rounded-full',
  }
  
  // Interactive styles
  const interactiveStyles = interactive || onClick || href ? [
    'cursor-pointer',
    'transition-all duration-200',
    hover && 'hover:scale-[1.02]',
    !disabled && 'focus:outline-none focus:ring-2 focus:ring-offset-2',
    !disabled && `focus:${themeStyles.border.focus}`,
  ].filter(Boolean).join(' ') : ''
  
  // Disabled styles
  const disabledStyles = disabled ? 'opacity-50 cursor-not-allowed' : ''
  
  // Loading styles
  const loadingStyles = loading ? 'relative overflow-hidden' : ''
  
  // Combine all styles
  const combinedClassName = cn(
    'relative',
    variantStyles[variant],
    sizeStyles[size],
    paddingStyles[padding],
    radiusStyles[radius],
    interactiveStyles,
    disabledStyles,
    loadingStyles,
    className
  )
  
  // Animation variants
  const cardVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.3, ease: 'easeOut' }
    },
    hover: hover && !disabled ? { 
      y: -2,
      transition: { duration: 0.2, ease: 'easeOut' }
    } : {},
    tap: interactive && !disabled ? { 
      scale: 0.98,
      transition: { duration: 0.1, ease: 'easeInOut' }
    } : {},
  }
  
  // Loading overlay
  const LoadingOverlay = () => (
    <div className="absolute inset-0 bg-white/50 dark:bg-black/50 flex items-center justify-center rounded-inherit">
      <motion.div
        className="w-6 h-6 border-2 border-current border-t-transparent rounded-full"
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
      />
    </div>
  )
  
  // Handle click
  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (disabled || loading) return
    onClick?.(event)
  }
  
  // Card content
  const CardContent = () => (
    <>
      {/* Header */}
      {header && (
        <div className="mb-4 pb-4 border-b border-current/10">
          {header}
        </div>
      )}
      
      {/* Main content */}
      <div className="flex-1">
        {children}
      </div>
      
      {/* Footer */}
      {footer && (
        <div className="mt-4 pt-4 border-t border-current/10">
          {footer}
        </div>
      )}
      
      {/* Loading overlay */}
      {loading && <LoadingOverlay />}
    </>
  )
  
  // Render as link if href is provided
  if (href && !disabled) {
    return (
      <motion.div
        ref={ref}
        className={combinedClassName}
        variants={animate ? cardVariants : undefined}
        initial={animate ? 'initial' : undefined}
        animate={animate ? 'animate' : undefined}
        whileHover={animate ? 'hover' : undefined}
        whileTap={animate ? 'tap' : undefined}
        onClick={handleClick}
        role={role || 'link'}
        aria-label={ariaLabel}
        tabIndex={0}
        {...motionProps}
      >
        <a href={href} className="block h-full">
          <CardContent />
        </a>
      </motion.div>
    )
  }
  
  // Render as interactive div
  return (
    <motion.div
      ref={ref}
      className={combinedClassName}
      variants={animate ? cardVariants : undefined}
      initial={animate ? 'initial' : undefined}
      animate={animate ? 'animate' : undefined}
      whileHover={animate ? 'hover' : undefined}
      whileTap={animate ? 'tap' : undefined}
      onClick={handleClick}
      role={role || (interactive ? 'button' : undefined)}
      aria-label={ariaLabel}
      aria-disabled={disabled}
      tabIndex={interactive && !disabled ? 0 : undefined}
      {...motionProps}
    >
      <CardContent />
    </motion.div>
  )
})

Card.displayName = 'Card'

export default Card
