import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ThemeProvider } from '@/context/ThemeContext'
import ContactPage from '../ContactPage'

// Test wrapper with theme context
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider>{children}</ThemeProvider>
)

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    section: ({ children, ...props }: any) => <section {...props}>{children}</section>,
    h1: ({ children, ...props }: any) => <h1 {...props}>{children}</h1>,
    h2: ({ children, ...props }: any) => <h2 {...props}>{children}</h2>,
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
    form: ({ children, ...props }: any) => <form {...props}>{children}</form>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    span: ({ children, ...props }: any) => <span {...props}>{children}</span>,
  },
  AnimatePresence: ({ children }: any) => children,
}))

// Mock EmailJS
jest.mock('@emailjs/browser', () => ({
  init: jest.fn(),
  sendForm: jest.fn(() => Promise.resolve({ status: 200, text: 'OK' })),
}))

describe('ContactPage - ATLAS v2.4 Refactored', () => {
  describe('Page Structure', () => {
    it('renders all main sections', () => {
      render(
        <TestWrapper>
          <ContactPage />
        </TestWrapper>
      )
      
      // Check for hero section
      expect(screen.getByText(/Conecta con/i)).toBeInTheDocument()
      expect(screen.getByText(/Nuestro Equipo/i)).toBeInTheDocument()
      
      // Check for form section
      expect(screen.getByText(/Envíanos un mensaje/i)).toBeInTheDocument()
      
      // Check for contact info section
      expect(screen.getByText(/Información de contacto/i)).toBeInTheDocument()
    })

    it('has proper semantic structure', () => {
      render(
        <TestWrapper>
          <ContactPage />
        </TestWrapper>
      )
      
      // Check for proper headings hierarchy
      const mainHeading = screen.getByRole('heading', { level: 1 })
      expect(mainHeading).toBeInTheDocument()
      
      const subHeadings = screen.getAllByRole('heading', { level: 2 })
      expect(subHeadings.length).toBeGreaterThan(0)
    })
  })

  describe('Contact Form Integration', () => {
    it('renders contact form with all required fields', () => {
      render(
        <TestWrapper>
          <ContactPage />
        </TestWrapper>
      )
      
      // Check for form fields
      expect(screen.getByLabelText(/nombre completo/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/mensaje/i)).toBeInTheDocument()
      
      // Check for submit button
      expect(screen.getByRole('button', { name: /enviar mensaje/i })).toBeInTheDocument()
    })

    it('handles form submission', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ContactPage />
        </TestWrapper>
      )
      
      // Fill out form
      await user.type(screen.getByLabelText(/nombre completo/i), 'Test User')
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/mensaje/i), 'This is a test message')
      
      // Submit form
      const submitButton = screen.getByRole('button', { name: /enviar mensaje/i })
      await user.click(submitButton)
      
      // Check for loading state
      expect(screen.getByText(/enviando mensaje/i)).toBeInTheDocument()
    })

    it('validates required fields', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ContactPage />
        </TestWrapper>
      )
      
      // Try to submit empty form
      const submitButton = screen.getByRole('button', { name: /enviar mensaje/i })
      await user.click(submitButton)
      
      // Check for validation messages
      await waitFor(() => {
        expect(screen.getByText(/nombre es requerido/i)).toBeInTheDocument()
      })
    })
  })

  describe('Contact Information', () => {
    it('displays contact information', () => {
      render(
        <TestWrapper>
          <ContactPage />
        </TestWrapper>
      )
      
      // Check for contact details
      expect(screen.getByText(/<EMAIL>/i)).toBeInTheDocument()
      expect(screen.getByText(/Madrid, España/i)).toBeInTheDocument()
    })

    it('has clickable contact links', () => {
      render(
        <TestWrapper>
          <ContactPage />
        </TestWrapper>
      )
      
      // Check for email link
      const emailLink = screen.getByRole('link', { name: /email/i })
      expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>')
      
      // Check for phone link
      const phoneLink = screen.getByRole('link', { name: /teléfono/i })
      expect(phoneLink).toHaveAttribute('href', 'tel:+34123456789')
    })
  })

  describe('Neural Background Animation', () => {
    it('renders neural background component', () => {
      render(
        <TestWrapper>
          <ContactPage />
        </TestWrapper>
      )
      
      // Check for SVG elements (neural background)
      const svgElements = document.querySelectorAll('svg')
      expect(svgElements.length).toBeGreaterThan(0)
    })
  })

  describe('Responsive Design', () => {
    it('adapts layout for different screen sizes', () => {
      render(
        <TestWrapper>
          <ContactPage />
        </TestWrapper>
      )
      
      // Check for responsive grid classes
      const gridContainer = document.querySelector('.grid')
      expect(gridContainer).toHaveClass('grid-cols-1', 'lg:grid-cols-2')
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      render(
        <TestWrapper>
          <ContactPage />
        </TestWrapper>
      )
      
      // Check for form accessibility
      const form = screen.getByRole('form')
      expect(form).toBeInTheDocument()
      
      // Check for proper labeling
      const nameInput = screen.getByLabelText(/nombre completo/i)
      expect(nameInput).toHaveAttribute('required')
      
      const emailInput = screen.getByLabelText(/email/i)
      expect(emailInput).toHaveAttribute('type', 'email')
    })

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ContactPage />
        </TestWrapper>
      )
      
      // Tab through form elements
      await user.tab()
      expect(screen.getByLabelText(/nombre completo/i)).toHaveFocus()
      
      await user.tab()
      expect(screen.getByLabelText(/email/i)).toHaveFocus()
    })
  })

  describe('Theme Integration', () => {
    it('adapts to theme changes', () => {
      render(
        <TestWrapper>
          <ContactPage />
        </TestWrapper>
      )
      
      // Component should render without errors in both themes
      expect(screen.getByText(/Conecta con/i)).toBeInTheDocument()
    })
  })

  describe('Performance', () => {
    it('renders efficiently without unnecessary re-renders', () => {
      const { rerender } = render(
        <TestWrapper>
          <ContactPage />
        </TestWrapper>
      )
      
      // Re-render with same props should not cause issues
      rerender(
        <TestWrapper>
          <ContactPage />
        </TestWrapper>
      )
      
      expect(screen.getByText(/Conecta con/i)).toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    it('handles form submission errors gracefully', async () => {
      // Mock EmailJS to reject
      const emailjs = require('@emailjs/browser')
      emailjs.sendForm.mockRejectedValueOnce(new Error('Network error'))
      
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ContactPage />
        </TestWrapper>
      )
      
      // Fill and submit form
      await user.type(screen.getByLabelText(/nombre completo/i), 'Test User')
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/mensaje/i), 'Test message')
      
      const submitButton = screen.getByRole('button', { name: /enviar mensaje/i })
      await user.click(submitButton)
      
      // Check for error message
      await waitFor(() => {
        expect(screen.getByText(/error al enviar/i)).toBeInTheDocument()
      })
    })
  })
})
