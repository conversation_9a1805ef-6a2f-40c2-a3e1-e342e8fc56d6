'use client';

import { Geist, <PERSON>eist_Mono, Inter, Outfit } from 'next/font/google';
import './globals.css';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { useEffect } from 'react';
import PageTransition from '@/components/ui/PageTransition';
import { ThemeProvider } from '@/context/ThemeContext';
import { CriticalCSS, ResourceHints, PerformanceObserver, ServiceWorkerRegistration } from '@/shared/components/CriticalCSS';

// Load fonts
const outfit = Outfit({
  variable: '--font-outfit',
  subsets: ['latin'],
  display: 'swap',
  weight: ['300', '400', '500', '600', '700', '800'],
});

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
  display: 'swap',
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
  display: 'swap',
});

const inter = Inter({
  variable: '--font-inter',
  subsets: ['latin'],
  display: 'swap',
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Forzar recarga de estilos en desarrollo
  useEffect(() => {
    if (process.env.NEXT_PUBLIC_FORCE_REFRESH === 'true') {
      const style = document.createElement('style');
      document.head.appendChild(style);
      document.head.removeChild(style);
    }
  }, []);

  return (
    <html lang='es' className='scroll-smooth'>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
        <meta name="description" content="Transformamos empresas a través del poder de la inteligencia artificial. Servicios de IA, formación, consultoría y desarrollo personalizado." />
        <meta name="keywords" content="inteligencia artificial, IA, machine learning, consultoría, formación, desarrollo" />
        <meta name="author" content="InformatiK-AI Team" />
        <meta name="robots" content="index, follow" />
        <meta name="theme-color" content="#00B4DB" />

        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://api.emailjs.com" />

        {/* DNS prefetch for performance */}
        <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
        <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
        <link rel="dns-prefetch" href="https://api.emailjs.com" />

        {/* Preload critical resources */}
        <link rel="preload" href="/fonts/outfit-variable.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />

        {/* PWA manifest */}
        <link rel="manifest" href="/manifest.json" />

        {/* Favicon and icons */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icons/icon-192x192.png" type="image/png" sizes="192x192" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />

        {/* Open Graph */}
        <meta property="og:type" content="website" />
        <meta property="og:locale" content="es_ES" />
        <meta property="og:title" content="InformatiK-AI - Soluciones de Inteligencia Artificial" />
        <meta property="og:description" content="Transformamos empresas a través del poder de la inteligencia artificial." />
        <meta property="og:site_name" content="InformatiK-AI" />

        {/* Twitter Card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="InformatiK-AI - Soluciones de Inteligencia Artificial" />
        <meta name="twitter:description" content="Transformamos empresas a través del poder de la inteligencia artificial." />

        <title>InformatiK-AI - Soluciones de Inteligencia Artificial</title>
      </head>
      <ThemeProvider>
        <body
          className={`${outfit.variable} ${geistSans.variable} ${geistMono.variable} ${inter.variable} font-outfit antialiased min-h-screen flex flex-col`}
        >
          <CriticalCSS />
          <ResourceHints />
          <PerformanceObserver />
          <ServiceWorkerRegistration />

          <Header />
          <main className='flex-grow'>
            <PageTransition>{children}</PageTransition>
          </main>
          <Footer />
        </body>
      </ThemeProvider>
    </html>
  );
}
