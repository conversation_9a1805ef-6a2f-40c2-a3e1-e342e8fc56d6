{
  "extends": [
    "next/core-web-vitals",
    "plugin:react-hooks/recommended",
    "plugin:jsx-a11y/recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:storybook/recommended",
    "prettier"
  ],
  "plugins": [
    "react-hooks",
    "jsx-a11y",
    "@typescript-eslint"
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": 2021,
    "sourceType": "module",
    "ecmaFeatures": {
      "jsx": true
    }
  },
  "rules": {
    // React rules
    "react/no-unescaped-entities": "off",
    "react/prop-types": "off",
    "react/react-in-jsx-scope": "off",
    "react/display-name": "warn",

    // React Hooks rules
    "react-hooks/exhaustive-deps": "warn",
    "react-hooks/rules-of-hooks": "error",

    // TypeScript rules
    "@typescript-eslint/no-unused-vars": ["error", { "argsIgnorePattern": "^_" }],
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/prefer-const": "error",
    "@typescript-eslint/no-var-requires": "error",

    // General JavaScript rules
    "prefer-const": "error",
    "no-var": "error",
    "no-console": "warn",
    "no-debugger": "error",
    "no-duplicate-imports": "error",
    "no-unused-vars": "off", // Handled by @typescript-eslint/no-unused-vars

    // Import rules
    "import/order": [
      "error",
      {
        "groups": [
          "builtin",
          "external",
          "internal",
          "parent",
          "sibling",
          "index"
        ],
        "newlines-between": "always",
        "alphabetize": {
          "order": "asc",
          "caseInsensitive": true
        }
      }
    ],

    // Accessibility rules
    "jsx-a11y/alt-text": "error",
    "jsx-a11y/anchor-is-valid": "error",
    "jsx-a11y/click-events-have-key-events": "warn",
    "jsx-a11y/no-static-element-interactions": "warn",

    // Performance rules
    "react/jsx-no-bind": ["warn", { "allowArrowFunctions": true }],
    "react/jsx-no-leaked-render": "error"
  },
  "overrides": [
    {
      "files": ["**/*.stories.@(js|jsx|ts|tsx)"],
      "rules": {
        "import/no-anonymous-default-export": "off",
        "@typescript-eslint/no-explicit-any": "off"
      }
    },
    {
      "files": ["**/*.test.@(js|jsx|ts|tsx)", "**/__tests__/**/*"],
      "rules": {
        "@typescript-eslint/no-explicit-any": "off",
        "react/display-name": "off"
      }
    }
  ],
  "settings": {
    "react": {
      "version": "detect"
    }
  }
}
