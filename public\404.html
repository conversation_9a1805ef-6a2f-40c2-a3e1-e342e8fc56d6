<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Página no encontrada | Informatik-AI</title>
    <link rel="icon" href="/favicon.ico" />
    <style>
        :root {
            --primary-color: #00B4DB;
            --secondary-color: #48D1CC;
            --dark-bg: #0f172a;
            --text-color: #f8fafc;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: var(--dark-bg);
            color: var(--text-color);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            position: relative;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 800px;
            text-align: center;
            z-index: 10;
            padding: 2rem;
        }
        
        h1 {
            font-size: 8rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .divider {
            height: 4px;
            width: 100px;
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            margin: 1.5rem auto;
            border-radius: 2px;
        }
        
        h2 {
            font-size: 2rem;
            margin-bottom: 1.5rem;
        }
        
        p {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            color: rgba(248, 250, 252, 0.8);
        }
        
        .buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        @media (min-width: 640px) {
            .buttons {
                flex-direction: row;
                justify-content: center;
            }
        }
        
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            text-decoration: none;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px -5px rgba(0, 180, 219, 0.4);
        }
        
        .btn-secondary {
            background: white;
            color: var(--dark-bg);
        }
        
        .btn-secondary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px -5px rgba(255, 255, 255, 0.3);
        }
        
        /* Background effects */
        .bg-effect {
            position: absolute;
            border-radius: 50%;
            filter: blur(100px);
            z-index: 1;
        }
        
        .bg-effect-1 {
            top: 0;
            left: 25%;
            width: 50%;
            height: 33%;
            background-color: rgba(0, 180, 219, 0.2);
        }
        
        .bg-effect-2 {
            bottom: 25%;
            right: 25%;
            width: 400px;
            height: 400px;
            background-color: rgba(72, 209, 204, 0.15);
        }
        
        /* Grid pattern */
        .grid-pattern {
            position: absolute;
            inset: 0;
            background-image: linear-gradient(to right, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
                              linear-gradient(to bottom, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
            background-size: 30px 30px;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="bg-effect bg-effect-1"></div>
    <div class="bg-effect bg-effect-2"></div>
    <div class="grid-pattern"></div>
    
    <div class="container">
        <h1>404</h1>
        <div class="divider"></div>
        <h2>Página no encontrada</h2>
        <p>Lo sentimos, la página que estás buscando no existe o ha sido movida.</p>
        
        <div class="buttons">
            <a href="/" class="btn btn-primary">Volver al Inicio</a>
            <a href="/contact" class="btn btn-secondary">Contactar Soporte</a>
        </div>
    </div>
    
    <script>
        // Redirect to home page if JavaScript is enabled and the URL has a hash
        document.addEventListener('DOMContentLoaded', function() {
            // Check if we have a route in the URL (after the domain)
            const path = window.location.pathname;
            if (path !== '/' && path !== '/404.html' && path !== '/404/') {
                // Store the current path in session storage
                sessionStorage.setItem('notFoundRedirect', path);
            }
        });
    </script>
</body>
</html>
