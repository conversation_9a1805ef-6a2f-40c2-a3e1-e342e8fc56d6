/**
 * ATLAS v2.4 Design System - Main Export
 * Centralized exports for the unified design system
 */

// Design Tokens
export * from './tokens/colors'
export * from './tokens/typography'
export * from './tokens/spacing'
export * from './tokens/animations'

// Hooks
export { default as useDesignTokens, useButtonStyles, useTextStyles, useResponsiveSpacing } from './hooks/useDesignTokens'
export { default as useThemeStyles, useComponentThemeStyles, useGradientStyles, useShadowStyles, useThemeState } from './hooks/useThemeStyles'

// Components
export { default as Button } from './components/Button'
export type { ButtonProps } from './components/Button'

// Re-export commonly used types
export type {
  ColorToken,
  ThemeColors,
  GradientToken,
  ShadowToken,
  FontFamily,
  FontWeight,
  FontSize,
  TextStyle,
  LetterSpacing,
  SpacingKey,
  BorderRadiusKey,
  BorderWidthKey,
  ZIndexKey,
  BreakpointKey,
  ContainerSizeKey,
  DurationKey,
  EasingKey,
  AnimationVariant,
  CSSAnimationKeyframe,
} from './tokens/colors'
