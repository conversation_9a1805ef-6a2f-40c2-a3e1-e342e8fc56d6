'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Mail, Phone, MapPin, Clock, MessageCircle } from 'lucide-react'
import { useThemeStyles } from '@/shared/ui-system'

interface ContactInfoProps {
  className?: string
}

/**
 * Contact Information Component
 * Displays contact details and business information
 * Extracted from contact/page.tsx for better modularity
 */
const ContactInfo: React.FC<ContactInfoProps> = ({ className = '' }) => {
  const themeStyles = useThemeStyles()
  
  // Contact information data
  const contactItems = [
    {
      icon: Mail,
      label: 'Email',
      value: '<EMAIL>',
      href: 'mailto:<EMAIL>',
      description: 'Respuesta en 24 horas',
    },
    {
      icon: Phone,
      label: 'Teléfono',
      value: '+**************',
      href: 'tel:+***********',
      description: 'Lunes a Viernes, 9:00 - 18:00',
    },
    {
      icon: MapPin,
      label: 'Ubicación',
      value: 'Madrid, España',
      href: null,
      description: 'Servicios en toda España',
    },
    {
      icon: Clock,
      label: 'Horario',
      value: '9:00 - 18:00',
      href: null,
      description: 'Lunes a Viernes',
    },
  ]
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  }
  
  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.4 },
    },
  }
  
  return (
    <motion.div
      className={`${themeStyles.background.elevated} rounded-2xl p-8 shadow-2xl ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header */}
      <motion.div variants={itemVariants} className="mb-8">
        <h2 className={`text-3xl font-bold mb-4 ${themeStyles.text.primary}`}>
          Información de contacto
        </h2>
        <p className={`text-lg ${themeStyles.text.secondary}`}>
          Estamos aquí para ayudarte. Ponte en contacto con nosotros a través de cualquiera de estos medios.
        </p>
      </motion.div>
      
      {/* Contact Items */}
      <div className="space-y-6">
        {contactItems.map((item, index) => {
          const Icon = item.icon
          
          const content = (
            <motion.div
              variants={itemVariants}
              className={`
                flex items-start space-x-4 p-4 rounded-lg transition-all duration-300
                ${themeStyles.interactive.hover}
                ${item.href ? 'cursor-pointer' : ''}
              `}
              whileHover={item.href ? { scale: 1.02 } : {}}
              whileTap={item.href ? { scale: 0.98 } : {}}
            >
              {/* Icon */}
              <div className={`
                flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center
                bg-gradient-to-r from-[#00B4DB] to-[#48D1CC]
              `}>
                <Icon className="w-6 h-6 text-white" />
              </div>
              
              {/* Content */}
              <div className="flex-1">
                <h3 className={`font-semibold text-lg ${themeStyles.text.primary} mb-1`}>
                  {item.label}
                </h3>
                <p className={`text-lg ${themeStyles.brand.primary} font-medium mb-1`}>
                  {item.value}
                </p>
                <p className={`text-sm ${themeStyles.text.muted}`}>
                  {item.description}
                </p>
              </div>
            </motion.div>
          )
          
          // Wrap in link if href is provided
          if (item.href) {
            return (
              <a
                key={index}
                href={item.href}
                className="block"
                aria-label={`${item.label}: ${item.value}`}
              >
                {content}
              </a>
            )
          }
          
          return <div key={index}>{content}</div>
        })}
      </div>
      
      {/* Additional Info */}
      <motion.div
        variants={itemVariants}
        className={`mt-8 p-6 rounded-lg ${themeStyles.background.primary} border ${themeStyles.border.primary}`}
      >
        <div className="flex items-start space-x-3">
          <MessageCircle className={`w-6 h-6 ${themeStyles.brand.primary} flex-shrink-0 mt-1`} />
          <div>
            <h4 className={`font-semibold ${themeStyles.text.primary} mb-2`}>
              ¿Prefieres una consulta rápida?
            </h4>
            <p className={`text-sm ${themeStyles.text.secondary} mb-3`}>
              Programa una llamada de 15 minutos gratuita para discutir tu proyecto.
            </p>
            <a
              href="https://calendly.com/informatik-ai"
              target="_blank"
              rel="noopener noreferrer"
              className={`
                inline-flex items-center text-sm font-medium
                ${themeStyles.brand.primary} hover:underline
                transition-colors duration-200
              `}
            >
              Programar consulta gratuita →
            </a>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}

export default ContactInfo
