import { renderHook, act } from '@testing-library/react'

import { useTypingAnimation } from '../useTypingAnimation'

// Mock timers for testing
jest.useFakeTimers()

describe('useTypingAnimation', () => {
  afterEach(() => {
    jest.clearAllTimers()
  })

  afterAll(() => {
    jest.useRealTimers()
  })

  describe('Basic functionality', () => {
    it('should initialize with empty text', () => {
      const { result } = renderHook(() =>
        useTypingAnimation({
          phrases: ['Hello', 'World'],
        })
      )

      expect(result.current.currentText).toBe('')
      expect(result.current.isTyping).toBe(false)
      expect(result.current.isPaused).toBe(false)
    })

    it('should start typing the first phrase', () => {
      const { result } = renderHook(() =>
        useTypingAnimation({
          phrases: ['Hello'],
          typingSpeed: 100,
        })
      )

      // Fast-forward time to trigger typing
      act(() => {
        jest.advanceTimersByTime(100)
      })

      expect(result.current.currentText).toBe('H')
      expect(result.current.isTyping).toBe(true)

      // Continue typing
      act(() => {
        jest.advanceTimersByTime(100)
      })

      expect(result.current.currentText).toBe('He')
    })

    it('should complete typing a phrase', () => {
      const { result } = renderHook(() =>
        useTypingAnimation({
          phrases: ['Hi'],
          typingSpeed: 50,
        })
      )

      // Type 'H'
      act(() => {
        jest.advanceTimersByTime(50)
      })
      expect(result.current.currentText).toBe('H')

      // Type 'i'
      act(() => {
        jest.advanceTimersByTime(50)
      })
      expect(result.current.currentText).toBe('Hi')

      // Should pause after completing
      act(() => {
        jest.advanceTimersByTime(100)
      })
      expect(result.current.isPaused).toBe(true)
    })
  })

  describe('Deleting functionality', () => {
    it('should start deleting after pause duration', () => {
      const { result } = renderHook(() =>
        useTypingAnimation({
          phrases: ['Hi'],
          typingSpeed: 50,
          deletingSpeed: 30,
          pauseDuration: 200,
        })
      )

      // Complete typing
      act(() => {
        jest.advanceTimersByTime(100) // Type 'Hi'
      })
      expect(result.current.currentText).toBe('Hi')

      // Wait for pause duration
      act(() => {
        jest.advanceTimersByTime(200)
      })

      // Should start deleting
      act(() => {
        jest.advanceTimersByTime(30)
      })
      expect(result.current.currentText).toBe('H')

      // Complete deleting
      act(() => {
        jest.advanceTimersByTime(30)
      })
      expect(result.current.currentText).toBe('')
    })
  })

  describe('Multiple phrases', () => {
    it('should cycle through multiple phrases', () => {
      const { result } = renderHook(() =>
        useTypingAnimation({
          phrases: ['A', 'B'],
          typingSpeed: 50,
          deletingSpeed: 30,
          pauseDuration: 100,
        })
      )

      // Type first phrase 'A'
      act(() => {
        jest.advanceTimersByTime(50)
      })
      expect(result.current.currentText).toBe('A')
      expect(result.current.currentPhrase).toBe('A')

      // Pause and delete
      act(() => {
        jest.advanceTimersByTime(100 + 30)
      })
      expect(result.current.currentText).toBe('')

      // Move to second phrase and type 'B'
      act(() => {
        jest.advanceTimersByTime(200 + 50)
      })
      expect(result.current.currentText).toBe('B')
      expect(result.current.currentPhrase).toBe('B')
    })

    it('should loop back to first phrase when loop is true', () => {
      const { result } = renderHook(() =>
        useTypingAnimation({
          phrases: ['A', 'B'],
          typingSpeed: 50,
          deletingSpeed: 30,
          pauseDuration: 100,
          loop: true,
        })
      )

      // Complete full cycle: A -> delete -> B -> delete -> A
      act(() => {
        // Type A, pause, delete A, pause, type B, pause, delete B, pause, type A
        jest.advanceTimersByTime(50 + 100 + 30 + 200 + 50 + 100 + 30 + 200 + 50)
      })

      expect(result.current.currentText).toBe('A')
      expect(result.current.currentPhrase).toBe('A')
    })
  })

  describe('Configuration options', () => {
    it('should respect custom typing speed', () => {
      const { result } = renderHook(() =>
        useTypingAnimation({
          phrases: ['Test'],
          typingSpeed: 200,
        })
      )

      // Should not have typed anything yet with slower speed
      act(() => {
        jest.advanceTimersByTime(100)
      })
      expect(result.current.currentText).toBe('')

      // Should type after full duration
      act(() => {
        jest.advanceTimersByTime(100)
      })
      expect(result.current.currentText).toBe('T')
    })

    it('should respect custom deleting speed', () => {
      const { result } = renderHook(() =>
        useTypingAnimation({
          phrases: ['AB'],
          typingSpeed: 50,
          deletingSpeed: 200,
          pauseDuration: 100,
        })
      )

      // Complete typing
      act(() => {
        jest.advanceTimersByTime(100)
      })
      expect(result.current.currentText).toBe('AB')

      // Start deleting
      act(() => {
        jest.advanceTimersByTime(100)
      })

      // Should not have deleted yet with slower speed
      act(() => {
        jest.advanceTimersByTime(100)
      })
      expect(result.current.currentText).toBe('AB')

      // Should delete after full duration
      act(() => {
        jest.advanceTimersByTime(100)
      })
      expect(result.current.currentText).toBe('A')
    })
  })

  describe('Control methods', () => {
    it('should pause and resume animation', () => {
      const { result } = renderHook(() =>
        useTypingAnimation({
          phrases: ['Hello'],
          typingSpeed: 50,
        })
      )

      // Start typing
      act(() => {
        jest.advanceTimersByTime(50)
      })
      expect(result.current.currentText).toBe('H')

      // Pause
      act(() => {
        result.current.pause()
      })
      expect(result.current.isPaused).toBe(true)

      // Should not continue typing while paused
      act(() => {
        jest.advanceTimersByTime(100)
      })
      expect(result.current.currentText).toBe('H')

      // Resume
      act(() => {
        result.current.resume()
        jest.advanceTimersByTime(50)
      })
      expect(result.current.currentText).toBe('He')
    })

    it('should reset animation', () => {
      const { result } = renderHook(() =>
        useTypingAnimation({
          phrases: ['Hello', 'World'],
          typingSpeed: 50,
        })
      )

      // Type some text
      act(() => {
        jest.advanceTimersByTime(150)
      })
      expect(result.current.currentText).toBe('Hel')

      // Reset
      act(() => {
        result.current.reset()
      })

      expect(result.current.currentText).toBe('')
      expect(result.current.currentPhrase).toBe('Hello')
      expect(result.current.isTyping).toBe(false)
      expect(result.current.isPaused).toBe(false)
    })
  })

  describe('Progress tracking', () => {
    it('should track typing progress correctly', () => {
      const { result } = renderHook(() =>
        useTypingAnimation({
          phrases: ['Test'], // 4 characters
          typingSpeed: 50,
        })
      )

      // Type 'T' (1/4 = 0.25)
      act(() => {
        jest.advanceTimersByTime(50)
      })
      expect(result.current.progress).toBe(0.25)

      // Type 'Te' (2/4 = 0.5)
      act(() => {
        jest.advanceTimersByTime(50)
      })
      expect(result.current.progress).toBe(0.5)

      // Type 'Test' (4/4 = 1)
      act(() => {
        jest.advanceTimersByTime(100)
      })
      expect(result.current.progress).toBe(1)
    })
  })

  describe('Edge cases', () => {
    it('should handle empty phrases array', () => {
      const { result } = renderHook(() =>
        useTypingAnimation({
          phrases: [],
        })
      )

      expect(result.current.currentText).toBe('')
      expect(result.current.currentPhrase).toBe('')

      // Should not crash when advancing time
      act(() => {
        jest.advanceTimersByTime(1000)
      })

      expect(result.current.currentText).toBe('')
    })

    it('should handle single character phrases', () => {
      const { result } = renderHook(() =>
        useTypingAnimation({
          phrases: ['A'],
          typingSpeed: 50,
        })
      )

      act(() => {
        jest.advanceTimersByTime(50)
      })

      expect(result.current.currentText).toBe('A')
      expect(result.current.progress).toBe(1)
    })
  })
})
