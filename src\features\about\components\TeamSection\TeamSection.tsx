'use client'

import React from 'react'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { Linkedin, Github, Twitter } from 'lucide-react'
import { useThemeStyles } from '@/shared/ui-system'

interface TeamMember {
  id: string
  name: string
  position: string
  image: string
  bio: string
  skills: string[]
  social?: {
    linkedin?: string
    github?: string
    twitter?: string
  }
}

interface TeamSectionProps {
  className?: string
}

/**
 * Team Section Component
 * Extracted from about/page.tsx for better modularity
 */
const TeamSection: React.FC<TeamSectionProps> = ({ className = '' }) => {
  const themeStyles = useThemeStyles()
  
  // Team members data
  const teamMembers: TeamMember[] = [
    {
      id: 'jorge',
      name: '<PERSON>',
      position: 'Gerente General',
      image: '/images/nosotros/danielSalgado.jpg',
      bio: 'Líder visionario con más de 15 años de experiencia en tecnología y gestión empresarial.',
      skills: ['Liderazgo', 'Estrategia', 'Innovación'],
      social: {
        linkedin: 'https://linkedin.com/in/jorge-salgado',
      },
    },
    {
      id: 'camila',
      name: '<PERSON>ila Bañares',
      position: 'Chief Innovation Officer',
      image: '/images/nosotros/camidevai.jpg',
      bio: 'Experta en IA con doctorado en Machine Learning y experiencia en las principales empresas tech.',
      skills: ['Machine Learning', 'Deep Learning', 'NLP'],
      social: {
        linkedin: 'https://linkedin.com/in/camila-banares',
        github: 'https://github.com/camidevai',
      },
    },
    {
      id: 'gonzalo',
      name: 'Gonzalo Figueroa',
      position: 'Chief Information Officer',
      image: '/images/nosotros/gonzalofigueroa.jpg',
      bio: 'Arquitecto de sistemas con especialización en infraestructura cloud y seguridad.',
      skills: ['Cloud Architecture', 'DevOps', 'Cybersecurity'],
      social: {
        linkedin: 'https://linkedin.com/in/gonzalo-figueroa',
        github: 'https://github.com/gonzalofigueroa',
      },
    },
  ]
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  }
  
  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' },
    },
  }
  
  const cardVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.5, ease: 'easeOut' },
    },
  }
  
  return (
    <section className={`py-20 ${themeStyles.background.secondary} relative overflow-hidden ${className}`}>
      {/* Background effects */}
      <div className="absolute top-0 right-1/4 w-1/2 h-1/3 rounded-full filter blur-[120px] bg-cyan-500/10" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 relative z-10">
        <motion.div
          className="text-center mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: '-100px' }}
          variants={containerVariants}
        >
          <motion.h2 
            className="text-4xl font-bold mb-4 text-glow"
            variants={itemVariants}
          >
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-blue-300 to-blue-200">
              Nuestro Equipo
            </span>
          </motion.h2>
          <motion.p 
            className={`${themeStyles.text.secondary} max-w-3xl mx-auto`}
            variants={itemVariants}
          >
            Conoce a los expertos detrás de Informatik-AI
          </motion.p>
        </motion.div>
        
        {/* Team Members Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: '-100px' }}
          variants={containerVariants}
        >
          {teamMembers.map((member) => (
            <motion.div
              key={member.id}
              className={`
                ${themeStyles.background.elevated} 
                rounded-2xl p-6 shadow-lg
                transition-all duration-300
                hover:shadow-2xl
              `}
              variants={cardVariants}
              whileHover={{ 
                y: -10,
                transition: { duration: 0.3 }
              }}
            >
              {/* Member Image */}
              <div className="relative w-32 h-32 mx-auto mb-6 rounded-full overflow-hidden border-4 border-gradient-to-r from-[#00B4DB] to-[#48D1CC]">
                <Image
                  src={member.image}
                  alt={member.name}
                  fill
                  style={{ objectFit: 'cover' }}
                  className="transition-transform duration-300 hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
              </div>
              
              {/* Member Info */}
              <div className="text-center mb-4">
                <h3 className={`text-xl font-bold mb-2 ${themeStyles.text.primary}`}>
                  {member.name}
                </h3>
                <p className={`${themeStyles.brand.primary} font-medium mb-3`}>
                  {member.position}
                </p>
                <p className={`${themeStyles.text.secondary} text-sm leading-relaxed`}>
                  {member.bio}
                </p>
              </div>
              
              {/* Skills */}
              <div className="mb-4">
                <div className="flex flex-wrap gap-2 justify-center">
                  {member.skills.map((skill, index) => (
                    <span
                      key={index}
                      className={`
                        px-3 py-1 text-xs font-medium rounded-full
                        ${themeStyles.utils.isDark 
                          ? 'bg-blue-900/30 text-blue-300' 
                          : 'bg-blue-100 text-blue-700'
                        }
                      `}
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
              
              {/* Social Links */}
              {member.social && (
                <div className="flex justify-center space-x-4">
                  {member.social.linkedin && (
                    <a
                      href={member.social.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`
                        w-10 h-10 rounded-full flex items-center justify-center
                        ${themeStyles.interactive.hover}
                        transition-all duration-200
                      `}
                    >
                      <Linkedin className={`w-5 h-5 ${themeStyles.brand.primary}`} />
                    </a>
                  )}
                  {member.social.github && (
                    <a
                      href={member.social.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`
                        w-10 h-10 rounded-full flex items-center justify-center
                        ${themeStyles.interactive.hover}
                        transition-all duration-200
                      `}
                    >
                      <Github className={`w-5 h-5 ${themeStyles.brand.primary}`} />
                    </a>
                  )}
                  {member.social.twitter && (
                    <a
                      href={member.social.twitter}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`
                        w-10 h-10 rounded-full flex items-center justify-center
                        ${themeStyles.interactive.hover}
                        transition-all duration-200
                      `}
                    >
                      <Twitter className={`w-5 h-5 ${themeStyles.brand.primary}`} />
                    </a>
                  )}
                </div>
              )}
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}

export default TeamSection
