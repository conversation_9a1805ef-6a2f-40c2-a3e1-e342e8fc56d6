/**
 * ATLAS v2.4 Design Tokens - Colors
 * Centralized color system for consistent theming
 */

// Base color palette
export const baseColors = {
  // Primary brand colors
  primary: {
    50: '#e0fbff',
    100: '#b3f5ff',
    200: '#80efff',
    300: '#4de9ff',
    400: '#1ae3ff',
    500: '#00B4DB', // Main primary
    600: '#00a0c2',
    700: '#008ca9',
    800: '#007890',
    900: '#006477',
  },
  
  // Secondary brand colors
  secondary: {
    50: '#e6fffe',
    100: '#b3fffc',
    200: '#80fff9',
    300: '#4dfff6',
    400: '#1afff3',
    500: '#48D1CC', // Main secondary
    600: '#3ec0c0',
    700: '#34afb4',
    800: '#2a9ea8',
    900: '#208d9c',
  },
  
  // Accent colors
  accent: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9', // Main accent
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
  },
  
  // Neutral colors
  neutral: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
  },
  
  // Semantic colors
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },
  
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },
  
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },
  
  // Special colors
  white: '#ffffff',
  black: '#000000',
  transparent: 'transparent',
} as const

// Theme-specific color mappings
export const lightTheme = {
  // Background colors
  background: {
    primary: baseColors.white,
    secondary: baseColors.neutral[50],
    tertiary: baseColors.neutral[100],
    elevated: baseColors.white,
    overlay: 'rgba(0, 0, 0, 0.5)',
  },
  
  // Text colors
  text: {
    primary: baseColors.neutral[900],
    secondary: baseColors.neutral[700],
    tertiary: baseColors.neutral[500],
    inverse: baseColors.white,
    disabled: baseColors.neutral[400],
  },
  
  // Border colors
  border: {
    primary: baseColors.neutral[200],
    secondary: baseColors.neutral[300],
    focus: baseColors.primary[500],
    error: baseColors.error[500],
  },
  
  // Brand colors
  brand: {
    primary: baseColors.primary[500],
    secondary: baseColors.secondary[500],
    accent: baseColors.accent[500],
  },
} as const

export const darkTheme = {
  // Background colors
  background: {
    primary: baseColors.neutral[900],
    secondary: baseColors.neutral[800],
    tertiary: baseColors.neutral[700],
    elevated: baseColors.neutral[800],
    overlay: 'rgba(0, 0, 0, 0.7)',
  },
  
  // Text colors
  text: {
    primary: baseColors.neutral[50],
    secondary: baseColors.neutral[200],
    tertiary: baseColors.neutral[400],
    inverse: baseColors.neutral[900],
    disabled: baseColors.neutral[600],
  },
  
  // Border colors
  border: {
    primary: baseColors.neutral[700],
    secondary: baseColors.neutral[600],
    focus: baseColors.primary[400],
    error: baseColors.error[400],
  },
  
  // Brand colors (adjusted for dark theme)
  brand: {
    primary: baseColors.primary[400],
    secondary: baseColors.secondary[400],
    accent: baseColors.accent[400],
  },
} as const

// Gradient definitions
export const gradients = {
  primary: 'linear-gradient(135deg, #00B4DB 0%, #48D1CC 100%)',
  secondary: 'linear-gradient(135deg, #48D1CC 0%, #0ea5e9 100%)',
  accent: 'linear-gradient(135deg, #0ea5e9 0%, #14b8a6 100%)',
  neutral: 'linear-gradient(135deg, #64748b 0%, #334155 100%)',
  
  // Special gradients
  hero: 'linear-gradient(135deg, #00F0FF 0%, #48D1CC 50%, #00B4DB 100%)',
  button: 'linear-gradient(135deg, #00B4DB 0%, #48D1CC 100%)',
  card: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
} as const

// Shadow definitions
export const shadows = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
  
  // Brand shadows
  primary: '0 4px 14px 0 rgba(0, 180, 219, 0.2)',
  secondary: '0 4px 14px 0 rgba(72, 209, 204, 0.2)',
  accent: '0 4px 14px 0 rgba(14, 165, 233, 0.2)',
  
  // Glow effects
  glow: {
    primary: '0 0 20px rgba(0, 180, 219, 0.3)',
    secondary: '0 0 20px rgba(72, 209, 204, 0.3)',
    accent: '0 0 20px rgba(14, 165, 233, 0.3)',
  },
} as const

// Color utilities
export const colorUtils = {
  /**
   * Get color with opacity
   */
  withOpacity: (color: string, opacity: number): string => {
    if (color.startsWith('#')) {
      const hex = color.slice(1)
      const r = parseInt(hex.slice(0, 2), 16)
      const g = parseInt(hex.slice(2, 4), 16)
      const b = parseInt(hex.slice(4, 6), 16)
      return `rgba(${r}, ${g}, ${b}, ${opacity})`
    }
    return color
  },
  
  /**
   * Get contrasting text color
   */
  getContrastColor: (backgroundColor: string): string => {
    // Simple contrast calculation - in production, use a proper contrast library
    if (backgroundColor.includes('dark') || backgroundColor.includes('900') || backgroundColor.includes('800')) {
      return lightTheme.text.primary
    }
    return darkTheme.text.primary
  },
} as const

export type ColorToken = keyof typeof baseColors
export type ThemeColors = typeof lightTheme
export type GradientToken = keyof typeof gradients
export type ShadowToken = keyof typeof shadows
