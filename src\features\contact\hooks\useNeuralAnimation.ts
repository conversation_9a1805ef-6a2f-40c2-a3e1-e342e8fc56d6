/**
 * ATLAS v2.4 - Neural Animation Hook
 * Extracted from contact/page.tsx for reusability
 */

import { useState, useEffect, useCallback } from 'react'

// Types
export interface NeuralPoint {
  cx: number
  cy: number
  r: string
  duration: string
}

export interface NeuralLine {
  x1: number
  y1: number
  x2: number
  y2: number
}

export interface NeuralAnimationConfig {
  pointCount?: number
  lineCount?: number
  minDuration?: number
  maxDuration?: number
  pointRadius?: string
}

// Default configuration
const DEFAULT_CONFIG: Required<NeuralAnimationConfig> = {
  pointCount: 15,
  lineCount: 20,
  minDuration: 3,
  maxDuration: 8,
  pointRadius: '0.5',
}

/**
 * Custom hook for neural network background animation
 * Generates random points and lines for animated background effects
 */
export const useNeuralAnimation = (config: NeuralAnimationConfig = {}) => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config }
  
  const [neuralPoints, setNeuralPoints] = useState<NeuralPoint[]>([])
  const [neuralLines, setNeuralLines] = useState<NeuralLine[]>([])
  const [isInitialized, setIsInitialized] = useState(false)
  
  // Generate random points
  const generatePoints = useCallback((): NeuralPoint[] => {
    return Array.from({ length: finalConfig.pointCount }).map(() => ({
      cx: Math.random() * 100,
      cy: Math.random() * 100,
      r: finalConfig.pointRadius,
      duration: `${finalConfig.minDuration + Math.random() * (finalConfig.maxDuration - finalConfig.minDuration)}s`
    }))
  }, [finalConfig])
  
  // Generate random lines
  const generateLines = useCallback((): NeuralLine[] => {
    return Array.from({ length: finalConfig.lineCount }).map(() => ({
      x1: Math.random() * 100,
      y1: Math.random() * 100,
      x2: Math.random() * 100,
      y2: Math.random() * 100
    }))
  }, [finalConfig])
  
  // Initialize animation on client side only
  useEffect(() => {
    if (typeof window === 'undefined') return
    
    const points = generatePoints()
    const lines = generateLines()
    
    setNeuralPoints(points)
    setNeuralLines(lines)
    setIsInitialized(true)
  }, [generatePoints, generateLines])
  
  // Regenerate animation
  const regenerate = useCallback(() => {
    const points = generatePoints()
    const lines = generateLines()
    
    setNeuralPoints(points)
    setNeuralLines(lines)
  }, [generatePoints, generateLines])
  
  // Public API
  return {
    neuralPoints,
    neuralLines,
    isInitialized,
    regenerate,
    config: finalConfig,
  }
}

export default useNeuralAnimation
