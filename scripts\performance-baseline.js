#!/usr/bin/env node

/**
 * ATLAS v2.4 Performance Baseline Script
 * Establishes performance metrics baseline for monitoring
 */

const fs = require('fs');
const path = require('path');

// ATLAS v2.4 Performance Targets
const ATLAS_PERFORMANCE_TARGETS = {
  // Core Web Vitals
  LCP: 2.5, // Largest Contentful Paint (seconds)
  FID: 0.1, // First Input Delay (seconds)
  CLS: 0.1, // Cumulative Layout Shift
  
  // Additional Metrics
  FCP: 1.8, // First Contentful Paint (seconds)
  TTI: 3.8, // Time to Interactive (seconds)
  TBT: 0.2, // Total Blocking Time (seconds)
  
  // Bundle Metrics
  BUNDLE_SIZE: 1.5 * 1024 * 1024, // 1.5MB
  JS_SIZE: 500 * 1024, // 500KB
  CSS_SIZE: 100 * 1024, // 100KB
  
  // Build Metrics
  BUILD_TIME: 60, // seconds
  TYPE_CHECK_TIME: 30, // seconds
  LINT_TIME: 15, // seconds
};

// Colors for console output
const colors = {
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function getCurrentMetrics() {
  const metrics = {
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    atlas_version: '2.4',
    
    // Code Quality Metrics
    code_quality: {
      total_files: 0,
      total_lines: 0,
      components_over_200_lines: 0,
      test_coverage: 0,
      typescript_errors: 0,
      eslint_warnings: 0,
    },
    
    // Performance Metrics (estimated)
    performance: {
      estimated_fcp: 1.8,
      estimated_lcp: 2.5,
      estimated_cls: 0.1,
      estimated_tti: 3.8,
    },
    
    // Bundle Metrics
    bundle: {
      total_size: 0,
      js_size: 0,
      css_size: 0,
      image_size: 0,
    },
    
    // Build Metrics
    build: {
      build_time: 0,
      type_check_time: 0,
      lint_time: 0,
    }
  };

  return metrics;
}

function calculateCodeMetrics() {
  log('📊 Calculating code quality metrics...', 'blue');
  
  let totalFiles = 0;
  let totalLines = 0;
  let componentsOver200 = 0;

  function analyzeDirectory(dir) {
    if (!fs.existsSync(dir)) return;
    
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
        analyzeDirectory(filePath);
      } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
        totalFiles++;
        
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n').length;
        totalLines += lines;
        
        if (lines > 200) {
          componentsOver200++;
          log(`  ⚠️  Large component: ${filePath} (${lines} lines)`, 'yellow');
        }
      }
    });
  }

  analyzeDirectory('src');
  
  return {
    total_files: totalFiles,
    total_lines: totalLines,
    components_over_200_lines: componentsOver200,
  };
}

function measureBuildTime(command) {
  log(`⏱️  Measuring build time for: ${command}`, 'blue');
  
  const start = Date.now();
  
  try {
    require('child_process').execSync(command, { 
      stdio: 'pipe',
      timeout: 120000 // 2 minutes timeout
    });
    
    const duration = (Date.now() - start) / 1000;
    log(`  ✅ Completed in ${duration.toFixed(2)}s`, 'green');
    return duration;
  } catch (error) {
    const duration = (Date.now() - start) / 1000;
    log(`  ❌ Failed after ${duration.toFixed(2)}s`, 'red');
    return duration;
  }
}

function generateBaselineReport() {
  log('\n🚀 ATLAS v2.4 Performance Baseline Generation', 'bold');
  log('==============================================', 'blue');
  
  const metrics = getCurrentMetrics();
  
  // Calculate code metrics
  const codeMetrics = calculateCodeMetrics();
  metrics.code_quality = { ...metrics.code_quality, ...codeMetrics };
  
  // Measure build times
  log('\n⏱️  Measuring build performance...', 'bold');
  metrics.build.type_check_time = measureBuildTime('npx tsc --noEmit');
  metrics.build.lint_time = measureBuildTime('npx eslint src --ext .ts,.tsx');
  
  // Calculate bundle size if build exists
  const outDir = path.join(process.cwd(), 'out');
  if (fs.existsSync(outDir)) {
    log('\n📦 Analyzing existing build...', 'blue');
    
    function calculateDirSize(dir) {
      let size = 0;
      const files = fs.readdirSync(dir);
      
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          size += calculateDirSize(filePath);
        } else {
          const fileSize = stat.size;
          size += fileSize;
          
          const ext = path.extname(file).toLowerCase();
          if (ext === '.js') metrics.bundle.js_size += fileSize;
          else if (ext === '.css') metrics.bundle.css_size += fileSize;
          else if (['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp'].includes(ext)) {
            metrics.bundle.image_size += fileSize;
          }
        }
      });
      
      return size;
    }
    
    metrics.bundle.total_size = calculateDirSize(outDir);
  }
  
  // Generate report
  log('\n📋 Baseline Report:', 'bold');
  log('==================', 'blue');
  
  log(`\n📊 Code Quality:`, 'bold');
  log(`  Total Files: ${metrics.code_quality.total_files}`);
  log(`  Total Lines: ${metrics.code_quality.total_lines}`);
  log(`  Components >200 lines: ${metrics.code_quality.components_over_200_lines}`, 
      metrics.code_quality.components_over_200_lines > 0 ? 'yellow' : 'green');
  
  log(`\n📦 Bundle Size:`, 'bold');
  log(`  Total: ${(metrics.bundle.total_size / 1024 / 1024).toFixed(2)} MB`);
  log(`  JavaScript: ${(metrics.bundle.js_size / 1024).toFixed(2)} KB`);
  log(`  CSS: ${(metrics.bundle.css_size / 1024).toFixed(2)} KB`);
  log(`  Images: ${(metrics.bundle.image_size / 1024).toFixed(2)} KB`);
  
  log(`\n⏱️  Build Performance:`, 'bold');
  log(`  Type Check: ${metrics.build.type_check_time.toFixed(2)}s`);
  log(`  Lint: ${metrics.build.lint_time.toFixed(2)}s`);
  
  // Save baseline
  const baselineFile = path.join(process.cwd(), 'atlas-baseline.json');
  fs.writeFileSync(baselineFile, JSON.stringify(metrics, null, 2));
  
  log(`\n💾 Baseline saved to: ${baselineFile}`, 'green');
  
  // Check against targets
  log(`\n🎯 ATLAS v2.4 Target Compliance:`, 'bold');
  
  const checks = [
    {
      name: 'Components >200 lines',
      current: metrics.code_quality.components_over_200_lines,
      target: 0,
      unit: 'components'
    },
    {
      name: 'Bundle size',
      current: metrics.bundle.total_size,
      target: ATLAS_PERFORMANCE_TARGETS.BUNDLE_SIZE,
      unit: 'bytes'
    },
    {
      name: 'Type check time',
      current: metrics.build.type_check_time,
      target: ATLAS_PERFORMANCE_TARGETS.TYPE_CHECK_TIME,
      unit: 'seconds'
    }
  ];
  
  checks.forEach(check => {
    const passed = check.current <= check.target;
    const status = passed ? '✅' : '❌';
    const color = passed ? 'green' : 'red';
    
    log(`  ${status} ${check.name}: ${check.current} ${check.unit} (target: ${check.target})`, color);
  });
  
  log('\n🎉 Baseline generation complete!', 'green');
  log('Use this baseline to track improvements over time.', 'blue');
}

// Run baseline generation
try {
  generateBaselineReport();
} catch (error) {
  log(`❌ Error generating baseline: ${error.message}`, 'red');
  process.exit(1);
}
