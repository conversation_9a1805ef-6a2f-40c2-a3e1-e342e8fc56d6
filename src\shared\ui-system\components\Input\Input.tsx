'use client'

import React, { forwardRef, useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { Eye, EyeOff, AlertCircle, CheckCircle, X } from 'lucide-react'
import { cn } from '@/utils/cn'
import { useThemeStyles } from '../../hooks/useThemeStyles'

// Types
export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  // Appearance
  variant?: 'default' | 'filled' | 'outlined' | 'underlined'
  size?: 'sm' | 'md' | 'lg'
  
  // Content
  label?: string
  placeholder?: string
  helperText?: string
  errorMessage?: string
  
  // Icons and addons
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  leftAddon?: React.ReactNode
  rightAddon?: React.ReactNode
  
  // State
  error?: boolean
  success?: boolean
  loading?: boolean
  clearable?: boolean
  
  // Behavior
  showPasswordToggle?: boolean
  autoResize?: boolean // for textarea-like behavior
  
  // Styling
  className?: string
  inputClassName?: string
  labelClassName?: string
  
  // Events
  onClear?: () => void
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void
}

/**
 * ATLAS v2.4 Input Component
 * 
 * Comprehensive input component with multiple variants, states, and accessibility
 * 
 * Features:
 * - 4 visual variants (default, filled, outlined, underlined)
 * - 3 sizes with design tokens
 * - Label, helper text, and error states
 * - Left/right icons and addons
 * - Password toggle functionality
 * - Clearable input option
 * - Loading state
 * - Full accessibility support
 * - Theme-aware styling
 * 
 * @param props - Input properties
 * @returns Input component
 */
const Input = forwardRef<HTMLInputElement, InputProps>(({
  // Appearance
  variant = 'default',
  size = 'md',
  
  // Content
  label,
  placeholder,
  helperText,
  errorMessage,
  
  // Icons and addons
  leftIcon,
  rightIcon,
  leftAddon,
  rightAddon,
  
  // State
  error = false,
  success = false,
  loading = false,
  clearable = false,
  
  // Behavior
  showPasswordToggle = false,
  type = 'text',
  
  // Styling
  className = '',
  inputClassName = '',
  labelClassName = '',
  
  // Events
  onClear,
  onFocus,
  onBlur,
  onChange,
  
  // Input props
  value,
  disabled,
  required,
  id,
  name,
  ...rest
}, ref) => {
  const themeStyles = useThemeStyles()
  const [isFocused, setIsFocused] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  
  // Generate unique ID if not provided
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`
  
  // Determine actual input type
  const actualType = showPasswordToggle && type === 'password' 
    ? (showPassword ? 'text' : 'password')
    : type
  
  // Determine state
  const hasError = error || !!errorMessage
  const hasValue = value !== undefined && value !== ''
  
  // Variant styles
  const variantStyles = {
    default: `
      ${themeStyles.background.secondary} 
      ${hasError ? 'border-red-500' : success ? 'border-green-500' : themeStyles.border.primary}
      border rounded-lg
      focus-within:${hasError ? 'border-red-500' : success ? 'border-green-500' : themeStyles.border.focus}
    `,
    filled: `
      ${themeStyles.utils.isDark ? 'bg-gray-800' : 'bg-gray-100'}
      ${hasError ? 'border-red-500' : success ? 'border-green-500' : 'border-transparent'}
      border rounded-lg
      focus-within:${hasError ? 'border-red-500' : success ? 'border-green-500' : themeStyles.border.focus}
    `,
    outlined: `
      ${themeStyles.background.secondary}
      ${hasError ? 'border-red-500' : success ? 'border-green-500' : themeStyles.border.secondary}
      border-2 rounded-lg
      focus-within:${hasError ? 'border-red-500' : success ? 'border-green-500' : themeStyles.border.focus}
    `,
    underlined: `
      bg-transparent
      ${hasError ? 'border-red-500' : success ? 'border-green-500' : themeStyles.border.primary}
      border-b-2 border-t-0 border-l-0 border-r-0 rounded-none
      focus-within:${hasError ? 'border-red-500' : success ? 'border-green-500' : themeStyles.border.focus}
    `,
  }
  
  // Size styles
  const sizeStyles = {
    sm: {
      container: 'text-sm',
      input: 'px-3 py-2 text-sm',
      icon: 'w-4 h-4',
      addon: 'px-3 text-sm',
    },
    md: {
      container: 'text-base',
      input: 'px-4 py-3 text-base',
      icon: 'w-5 h-5',
      addon: 'px-4 text-base',
    },
    lg: {
      container: 'text-lg',
      input: 'px-5 py-4 text-lg',
      icon: 'w-6 h-6',
      addon: 'px-5 text-lg',
    },
  }
  
  // Handle focus
  const handleFocus = useCallback((event: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true)
    onFocus?.(event)
  }, [onFocus])
  
  // Handle blur
  const handleBlur = useCallback((event: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false)
    onBlur?.(event)
  }, [onBlur])
  
  // Handle clear
  const handleClear = useCallback(() => {
    if (onChange) {
      const syntheticEvent = {
        target: { value: '' },
        currentTarget: { value: '' },
      } as React.ChangeEvent<HTMLInputElement>
      onChange(syntheticEvent)
    }
    onClear?.()
  }, [onChange, onClear])
  
  // Toggle password visibility
  const togglePasswordVisibility = useCallback(() => {
    setShowPassword(prev => !prev)
  }, [])
  
  // Animation variants
  const labelVariants = {
    default: {
      top: '50%',
      fontSize: sizeStyles[size].container,
      transform: 'translateY(-50%)',
    },
    focused: {
      top: '0%',
      fontSize: '0.75rem',
      transform: 'translateY(-50%)',
    },
  }
  
  return (
    <div className={cn('relative', className)}>
      {/* Label */}
      {label && (
        <motion.label
          htmlFor={inputId}
          className={cn(
            'absolute left-4 pointer-events-none transition-all duration-200 z-10',
            themeStyles.text.secondary,
            isFocused || hasValue ? 'text-xs -top-2 bg-inherit px-1' : '',
            hasError ? 'text-red-500' : success ? 'text-green-500' : '',
            disabled ? 'opacity-50' : '',
            labelClassName
          )}
          variants={labelVariants}
          animate={isFocused || hasValue ? 'focused' : 'default'}
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </motion.label>
      )}
      
      {/* Input container */}
      <div className={cn(
        'relative flex items-center transition-all duration-200',
        variantStyles[variant],
        disabled ? 'opacity-50 cursor-not-allowed' : '',
        sizeStyles[size].container
      )}>
        {/* Left addon */}
        {leftAddon && (
          <div className={cn(
            'flex items-center border-r',
            themeStyles.border.primary,
            themeStyles.text.secondary,
            sizeStyles[size].addon
          )}>
            {leftAddon}
          </div>
        )}
        
        {/* Left icon */}
        {leftIcon && (
          <div className={cn(
            'flex items-center justify-center ml-3',
            themeStyles.text.secondary,
            sizeStyles[size].icon
          )}>
            {leftIcon}
          </div>
        )}
        
        {/* Input */}
        <input
          ref={ref}
          id={inputId}
          name={name}
          type={actualType}
          value={value}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          className={cn(
            'flex-1 bg-transparent outline-none',
            themeStyles.text.primary,
            'placeholder:' + themeStyles.text.muted,
            sizeStyles[size].input,
            leftIcon || leftAddon ? 'pl-0' : '',
            rightIcon || rightAddon || clearable || showPasswordToggle ? 'pr-0' : '',
            disabled ? 'cursor-not-allowed' : '',
            inputClassName
          )}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onChange={onChange}
          {...rest}
        />
        
        {/* Right icons and addons */}
        <div className="flex items-center">
          {/* Success icon */}
          {success && !hasError && (
            <CheckCircle className={cn(
              'text-green-500 mr-2',
              sizeStyles[size].icon
            )} />
          )}
          
          {/* Error icon */}
          {hasError && (
            <AlertCircle className={cn(
              'text-red-500 mr-2',
              sizeStyles[size].icon
            )} />
          )}
          
          {/* Loading spinner */}
          {loading && (
            <motion.div
              className={cn(
                'border-2 border-current border-t-transparent rounded-full mr-2',
                sizeStyles[size].icon
              )}
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            />
          )}
          
          {/* Clear button */}
          {clearable && hasValue && !loading && (
            <button
              type="button"
              onClick={handleClear}
              className={cn(
                'flex items-center justify-center mr-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full p-1 transition-colors',
                themeStyles.text.secondary
              )}
              aria-label="Clear input"
            >
              <X className={sizeStyles[size].icon} />
            </button>
          )}
          
          {/* Password toggle */}
          {showPasswordToggle && type === 'password' && (
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className={cn(
                'flex items-center justify-center mr-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full p-1 transition-colors',
                themeStyles.text.secondary
              )}
              aria-label={showPassword ? 'Hide password' : 'Show password'}
            >
              {showPassword ? (
                <EyeOff className={sizeStyles[size].icon} />
              ) : (
                <Eye className={sizeStyles[size].icon} />
              )}
            </button>
          )}
          
          {/* Right icon */}
          {rightIcon && (
            <div className={cn(
              'flex items-center justify-center mr-3',
              themeStyles.text.secondary,
              sizeStyles[size].icon
            )}>
              {rightIcon}
            </div>
          )}
          
          {/* Right addon */}
          {rightAddon && (
            <div className={cn(
              'flex items-center border-l',
              themeStyles.border.primary,
              themeStyles.text.secondary,
              sizeStyles[size].addon
            )}>
              {rightAddon}
            </div>
          )}
        </div>
      </div>
      
      {/* Helper text or error message */}
      {(helperText || errorMessage) && (
        <motion.p
          className={cn(
            'mt-2 text-sm',
            hasError ? 'text-red-500' : themeStyles.text.secondary
          )}
          initial={{ opacity: 0, y: -5 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          {errorMessage || helperText}
        </motion.p>
      )}
    </div>
  )
})

Input.displayName = 'Input'

export default Input
