# 🤝 Guía de Contribución - InformatiK-AI

Bienvenido al proyecto InformatiK-AI. Esta guía te ayudará a contribuir siguiendo los principios de **ATLAS v2.4**.

## 🚀 Metodología ATLAS v2.4

Este proyecto sigue la metodología ATLAS v2.4 para desarrollo holístico de ventures digitales. Esto significa:

- **Arquitectura modular** con componentes especializados
- **Testing obligatorio** para todo código nuevo
- **Quality gates** automatizados
- **Documentación viva** con Storybook
- **Performance monitoring** continuo

## 📋 Antes de Empezar

### Prerrequisitos
- Node.js 18+ 
- npm 9+
- Git configurado
- Editor con soporte TypeScript (recomendado: VS Code)

### Configuración Inicial
```bash
# 1. Fork el repositorio en GitHub
# 2. Clona tu fork
git clone https://github.com/TU_USUARIO/informatik-ai.git
cd informatik-ai

# 3. Instala dependencias
npm install

# 4. Configura hooks de git
npm run prepare

# 5. Verifica que todo funciona
npm run quality
npm run test
npm run dev
```

## 🛠️ Flujo de Desarrollo

### 1. Crear Nueva Rama
```bash
# Para nuevas funcionalidades
git checkout -b feature/nombre-funcionalidad

# Para correcciones de bugs
git checkout -b fix/descripcion-bug

# Para refactorizaciones
git checkout -b refactor/componente-nombre
```

### 2. Desarrollo con ATLAS v2.4

#### Principios de Código
- **Máximo 200 líneas** por componente
- **Una responsabilidad** por componente
- **TypeScript strict** obligatorio
- **Tests unitarios** para lógica compleja
- **Storybook stories** para componentes UI

#### Estructura de Archivos
```
src/features/nueva-funcionalidad/
├── components/
│   ├── ComponentePrincipal/
│   │   ├── ComponentePrincipal.tsx
│   │   ├── ComponentePrincipal.test.tsx
│   │   ├── ComponentePrincipal.stories.tsx
│   │   └── index.ts
│   └── SubComponente/
├── hooks/
│   ├── useCustomHook.ts
│   └── __tests__/
└── utils/
```

### 3. Quality Checks

Antes de hacer commit, ejecuta:
```bash
# Verificación completa
npm run quality

# Corrección automática
npm run quality:fix

# Tests con cobertura
npm run test:coverage
```

### 4. Commits

Los **pre-commit hooks** se ejecutan automáticamente y verifican:
- ✅ Linting (ESLint)
- ✅ Formateo (Prettier)
- ✅ Type checking (TypeScript)
- ✅ Tests relacionados

#### Formato de Commits
```bash
# Ejemplos de buenos commits
git commit -m "feat: add useTypingAnimation hook with tests"
git commit -m "refactor: split HeroSection into modular components"
git commit -m "fix: resolve memory leak in animation cleanup"
git commit -m "docs: update README with new scripts"
git commit -m "test: add integration tests for contact form"
```

## 🧪 Testing Guidelines

### Tests Obligatorios
- **Hooks personalizados**: 100% cobertura
- **Componentes con lógica**: >80% cobertura
- **Utilidades**: 100% cobertura
- **Componentes UI**: Tests básicos de renderizado

### Estructura de Tests
```typescript
// ComponenteEjemplo.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import ComponenteEjemplo from './ComponenteEjemplo'

// Wrapper con contextos necesarios
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider>{children}</ThemeProvider>
)

describe('ComponenteEjemplo', () => {
  describe('Rendering', () => {
    it('renders without crashing', () => {
      render(
        <TestWrapper>
          <ComponenteEjemplo />
        </TestWrapper>
      )
      expect(screen.getByRole('button')).toBeInTheDocument()
    })
  })

  describe('Interactions', () => {
    it('handles click events', async () => {
      const handleClick = jest.fn()
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ComponenteEjemplo onClick={handleClick} />
        </TestWrapper>
      )
      
      await user.click(screen.getByRole('button'))
      expect(handleClick).toHaveBeenCalledTimes(1)
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(
        <TestWrapper>
          <ComponenteEjemplo aria-label="Test button" />
        </TestWrapper>
      )
      
      expect(screen.getByRole('button')).toHaveAttribute('aria-label', 'Test button')
    })
  })
})
```

### Ejecutar Tests
```bash
# Modo watch durante desarrollo
npm run test:watch

# Con cobertura
npm run test:coverage

# Para CI/CD
npm run test:ci
```

## 📚 Storybook Guidelines

### Stories Obligatorias
- **Componentes UI**: Todas las variantes
- **Estados**: Default, loading, error, disabled
- **Responsive**: Mobile, tablet, desktop
- **Temas**: Light y dark mode

### Estructura de Stories
```typescript
// ComponenteEjemplo.stories.tsx
import type { Meta, StoryObj } from '@storybook/react'
import ComponenteEjemplo from './ComponenteEjemplo'

const meta: Meta<typeof ComponenteEjemplo> = {
  title: 'UI/ComponenteEjemplo',
  component: ComponenteEjemplo,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Descripción del componente y su propósito.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary'],
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    children: 'Ejemplo',
  },
}

export const AllVariants: Story = {
  render: () => (
    <div className="flex gap-4">
      <ComponenteEjemplo variant="primary">Primary</ComponenteEjemplo>
      <ComponenteEjemplo variant="secondary">Secondary</ComponenteEjemplo>
    </div>
  ),
}
```

## 🎯 Quality Gates ATLAS

### Criterios de Aceptación
Para que tu PR sea aceptado debe cumplir:

#### ✅ Código
- [ ] Componente <200 líneas
- [ ] TypeScript strict compliance
- [ ] ESLint sin warnings
- [ ] Prettier aplicado
- [ ] Imports organizados

#### ✅ Testing
- [ ] Tests unitarios >80% cobertura
- [ ] Tests de integración si aplica
- [ ] Tests de accesibilidad básicos
- [ ] No regresiones en tests existentes

#### ✅ Documentación
- [ ] Storybook story implementada
- [ ] JSDoc en funciones complejas
- [ ] README actualizado si es necesario
- [ ] CHANGELOG.md actualizado

#### ✅ Performance
- [ ] Bundle size impact <50KB
- [ ] No memory leaks
- [ ] Lazy loading si es componente grande
- [ ] Optimizaciones de re-renders

## 🚨 Errores Comunes

### ❌ Evitar
```typescript
// Componente demasiado grande
const ComponenteGigante = () => {
  // 300+ líneas de código
  // Múltiples responsabilidades
  // Lógica compleja mezclada
}

// Sin tests
export default ComponenteSinTests

// Props sin tipos
const Componente = ({ props }: any) => {}

// Efectos sin cleanup
useEffect(() => {
  const interval = setInterval(callback, 1000)
  // ❌ Sin cleanup
}, [])
```

### ✅ Hacer
```typescript
// Componente modular
const ComponenteModular = ({ data }: ComponenteProps) => {
  const processedData = useProcessData(data)
  return <ComponenteUI data={processedData} />
}

// Con tests completos
// ComponenteModular.test.tsx existe

// Props tipadas
interface ComponenteProps {
  data: DataType
  onAction?: (id: string) => void
}

// Efectos con cleanup
useEffect(() => {
  const interval = setInterval(callback, 1000)
  return () => clearInterval(interval) // ✅ Cleanup
}, [])
```

## 📞 Soporte

### Canales de Comunicación
- **Issues**: Para bugs y feature requests
- **Discussions**: Para preguntas y propuestas
- **PR Reviews**: Para feedback de código

### Recursos Útiles
- [Documentación ATLAS v2.4](./ATLAS-TECHNICAL-ANALYSIS.md)
- [Storybook local](http://localhost:6006)
- [Métricas de performance](./atlas-baseline.json)

---

**¡Gracias por contribuir a InformatiK-AI! 🚀**

Siguiendo estos principios ATLAS v2.4, juntos construimos software de clase mundial.
