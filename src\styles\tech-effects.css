/* Estilos para efectos tecnológicos - Versión mejorada */

/* Efectos de texto */

/* Efecto de texto con resplandor - Versión mejorada y unificada */
.text-glow {
  text-shadow:
    0 0 5px rgba(37, 99, 235, 0.7),
    0 0 10px rgba(37, 99, 235, 0.5),
    0 0 15px rgba(37, 99, 235, 0.3);
}

.dark .text-glow {
  text-shadow:
    0 0 5px rgba(59, 130, 246, 0.7),
    0 0 10px rgba(59, 130, 246, 0.5),
    0 0 15px rgba(59, 130, 246, 0.3);
}

/* Animación de pulso para texto - Versión mejorada */
@keyframes text-pulse {
  0% {
    opacity: 1;
    text-shadow: 0 0 5px rgba(37, 99, 235, 0.7);
  }
  50% {
    opacity: 0.9;
    text-shadow:
      0 0 15px rgba(37, 99, 235, 1),
      0 0 20px rgba(37, 99, 235, 0.8);
  }
  100% {
    opacity: 1;
    text-shadow: 0 0 5px rgba(37, 99, 235, 0.7);
  }
}

@keyframes text-pulse-dark {
  0% {
    opacity: 1;
    text-shadow: 0 0 5px rgba(59, 130, 246, 0.7);
  }
  50% {
    opacity: 0.9;
    text-shadow:
      0 0 15px rgba(59, 130, 246, 1),
      0 0 20px rgba(59, 130, 246, 0.8);
  }
  100% {
    opacity: 1;
    text-shadow: 0 0 5px rgba(59, 130, 246, 0.7);
  }
}

.text-pulse {
  animation: text-pulse 3s infinite ease-in-out;
}

.dark .text-pulse {
  animation: text-pulse-dark 3s infinite ease-in-out;
}

/* Animación de glitch para texto - Versión más sutil */
@keyframes text-glitch {
  0% {
    transform: translate(0);
    text-shadow: 0 0 0 rgba(37, 99, 235, 0);
  }
  2% {
    transform: translate(-1px, 1px);
    text-shadow:
      -1px 0 1px rgba(37, 99, 235, 0.7),
      1px 0 1px rgba(14, 165, 233, 0.7);
  }
  4% {
    transform: translate(1px, -1px);
    text-shadow:
      1px 0 1px rgba(37, 99, 235, 0.7),
      -1px 0 1px rgba(14, 165, 233, 0.7);
  }
  5% {
    transform: translate(0);
    text-shadow: 0 0 0 rgba(37, 99, 235, 0);
  }
  100% {
    transform: translate(0);
    text-shadow: 0 0 0 rgba(37, 99, 235, 0);
  }
}

.text-glitch {
  position: relative;
}

.text-glitch::before,
.text-glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.text-glitch::before {
  left: 1px;
  text-shadow: -1px 0 rgba(37, 99, 235, 0.7);
  animation: text-glitch 5s infinite linear alternate-reverse;
}

.text-glitch::after {
  left: -1px;
  text-shadow: 1px 0 rgba(14, 165, 233, 0.7);
  animation: text-glitch 4s infinite linear alternate-reverse;
}

/* Efecto de escaneo para texto - Versión mejorada */
@keyframes text-scan {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

.text-scan {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(37, 99, 235, 0.8),
    transparent
  );
  background-size: 200% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  animation: text-scan 3s infinite linear;
}

.dark .text-scan {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.8),
    transparent
  );
}

/* Efectos de fondo y contenedores */

/* Efecto de resplandor para elementos destacados */
.glow-effect {
  position: relative;
}

.glow-effect::before {
  content: '';
  position: absolute;
  inset: -5px;
  background: radial-gradient(
    circle at 50% 50%,
    rgba(37, 99, 235, 0.3),
    transparent 70%
  );
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dark .glow-effect::before {
  background: radial-gradient(
    circle at 50% 50%,
    rgba(59, 130, 246, 0.3),
    transparent 70%
  );
}

.glow-effect:hover::before {
  opacity: 1;
}

/* Efecto de líneas de código para fondos */
.code-lines-bg {
  position: relative;
  overflow: hidden;
}

.code-lines-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: repeating-linear-gradient(
    0deg,
    rgba(255, 255, 255, 0.03) 0px,
    rgba(255, 255, 255, 0.03) 1px,
    transparent 1px,
    transparent 24px
  );
  pointer-events: none;
  z-index: 1;
}

.dark .code-lines-bg::before {
  background-image: repeating-linear-gradient(
    0deg,
    rgba(255, 255, 255, 0.05) 0px,
    rgba(255, 255, 255, 0.05) 1px,
    transparent 1px,
    transparent 24px
  );
}

/* Efecto de escaneo para secciones */
.scan-effect {
  position: relative;
  overflow: hidden;
}

.scan-effect::after {
  content: '';
  position: absolute;
  top: -100%;
  left: 0;
  right: 0;
  height: 200%;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(37, 99, 235, 0.1) 50%,
    transparent 100%
  );
  animation: scan 4s linear infinite;
  pointer-events: none;
}

.dark .scan-effect::after {
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(59, 130, 246, 0.1) 50%,
    transparent 100%
  );
}

@keyframes scan {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(100%);
  }
}

/* Efecto de fondo de matriz */
.matrix-bg {
  position: relative;
}

.matrix-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
    rgba(37, 99, 235, 0.1) 1px,
    transparent 1px
  );
  background-size: 20px 20px;
  pointer-events: none;
  z-index: 1;
}

.dark .matrix-bg::before {
  background-image: radial-gradient(
    rgba(59, 130, 246, 0.1) 1px,
    transparent 1px
  );
}

/* Efectos para elementos interactivos */

/* Efecto de partículas para botones */
.particle {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(37, 99, 235, 0.7);
  pointer-events: none;
  z-index: 10;
}

.dark .particle {
  background-color: rgba(59, 130, 246, 0.7);
}

/* Efecto de texto tecnológico */
.tech-text {
  position: relative;
  display: inline-block;
}

.tech-text::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: -2px;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(37, 99, 235, 0.7),
    transparent
  );
  transform: scaleX(0);
  transform-origin: center;
  transition: transform 0.3s ease;
}

.dark .tech-text::after {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.7),
    transparent
  );
}

.tech-text:hover::after {
  transform: scaleX(1);
}

/* Efecto de botón de datos */
.data-button {
  position: relative;
  overflow: hidden;
}

.data-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.7s ease;
}

.data-button:hover::before {
  left: 100%;
}

/* Efecto de borde brillante */
.glow-border {
  position: relative;
}

.glow-border::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 2px;
  background: linear-gradient(
    45deg,
    rgba(37, 99, 235, 0.5),
    rgba(14, 165, 233, 0.5)
  );
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dark .glow-border::after {
  background: linear-gradient(
    45deg,
    rgba(59, 130, 246, 0.5),
    rgba(14, 165, 233, 0.5)
  );
}

.glow-border:hover::after {
  opacity: 1;
}

/* Efecto de desenfoque de movimiento */
.motion-blur {
  transition:
    transform 0.3s ease,
    filter 0.3s ease;
}

.motion-blur:hover {
  filter: blur(0.5px);
  transform: scale(1.02);
}

/* Animación de parpadeo para el cursor */
@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

.animate-blink {
  animation: blink 0.75s step-end infinite;
}
