/**
 * Class name utility function
 * Combines and conditionally applies CSS classes
 */

import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

/**
 * Combines class names with clsx and merges Tailwind classes with tailwind-merge
 * 
 * @param inputs - Class values to combine
 * @returns Combined and merged class string
 */
export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs))
}

export default cn
