/* Estilos globales */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Importar estilos para efectos tecnológicos y red neural */
@import '../styles/tech-effects.css';
@import '../styles/neural-network.css';
@import '../styles/theme-utils.css';
@import '../styles/custom-cursors.css';

:root {
  /* Light mode variables - Nuevo tema */
  --background: #e0fbff; /* Calipso muy claro */
  --foreground: #111111; /* Texto principal casi negro */

  /* Modern color palette for Informatik-AI */
  --primary: #007d84; /* Calipso medio - Elementos destacados */
  --primary-dark: #006a70; /* Calipso medio oscuro - Hover */
  --primary-light: #00f0ff; /* Calipso vibrante - Acentos */

  --secondary: #00b4db; /* Calipso - Secondary brand color */
  --secondary-dark: #0099b8; /* Calipso oscuro - Hover */
  --secondary-light: #48d1cc; /* Calipso claro - Accents */

  /* Tooltip background */
  --tooltip-bg: rgba(15, 23, 42, 0.9);

  --accent: #00f0ff; /* Calipso vibrante - Accent color */
  --accent-dark: #00d6e4; /* Calipso vibrante oscuro - Hover */

  --success: #10b981; /* Emerald-500 - Success messages */
  --warning: #f59e0b; /* Amber-500 - Warning messages */
  --error: #ef4444; /* Red-500 - Error messages */

  --gray-light: #e0fbff; /* Calipso muy claro - Light background */
  --gray-medium: #444444; /* Gris oscuro - Subtítulos */
  --gray-dark: #111111; /* Casi negro - Texto principal */
  --gray-darker: #000000; /* Negro - Contraste máximo */

  /* Card and container backgrounds */
  --card-bg: #ffffff;
  --container-bg: #e0fbff;
  --header-bg: rgba(224, 251, 255, 0.8); /* Calipso muy claro con opacidad */
  --footer-bg: #d0f5ff;

  /* Borders */
  --border-color: #b0e5f0;
  --border-color-hover: #80d0e0;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md:
    0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg:
    0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Gradients */
  --gradient-primary: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--secondary) 100%
  );
  --gradient-cta: linear-gradient(
    135deg,
    var(--accent) 0%,
    var(--secondary) 100%
  );
  --gradient-hero: linear-gradient(
    135deg,
    var(--primary-dark) 0%,
    var(--primary) 50%,
    var(--secondary) 100%
  );
  --gradient-card: linear-gradient(to bottom right, var(--gray-light), white);

  /* Animation durations */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;

  /* Spacing system */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 1rem;
  --space-4: 1.5rem;
  --space-5: 2rem;
  --space-6: 3rem;
  --space-7: 4rem;
  --space-8: 6rem;
  --space-9: 8rem;
}

/* Dark mode variables - Nuevo tema oscuro */
.dark {
  --background: #000000; /* Negro puro */
  --foreground: #ffffff; /* Blanco puro */

  /* Adjusted colors for dark mode */
  --primary: #00f0ff; /* Calipso vibrante - Elementos primarios */
  --primary-dark: #00d6e4; /* Calipso vibrante oscuro - Hover */
  --primary-light: #48f0ff; /* Calipso vibrante claro - Accents */

  --secondary: #00b4db; /* Calipso - Secondary brand color */
  --secondary-dark: #0099b8; /* Calipso oscuro - Hover */
  --secondary-light: #48d1cc; /* Calipso claro - Accents */

  --accent: #00f0ff; /* Calipso vibrante - Accent color */
  --accent-dark: #00d6e4; /* Calipso vibrante oscuro - Hover */

  /* Tooltip background */
  --tooltip-bg: rgba(0, 0, 0, 0.9);

  --success: #34d399; /* Emerald-400 - Success messages */
  --warning: #fbbf24; /* Amber-400 - Warning messages */
  --error: #f87171; /* Red-400 - Error messages */

  --gray-light: #333333; /* Gris oscuro - Dark mode light gray */
  --gray-medium: #a0a0a0; /* Gris claro - Subtítulos */
  --gray-dark: #ffffff; /* Blanco - Dark mode dark gray */
  --gray-darker: #ffffff; /* Blanco - Dark mode darker gray */

  /* Card and container backgrounds */
  --card-bg: #111111; /* Casi negro */
  --container-bg: #000000; /* Negro puro */
  --header-bg: rgba(0, 0, 0, 0.8); /* Negro con opacidad */
  --footer-bg: #111111; /* Casi negro */

  /* Borders */
  --border-color: #333333; /* Gris oscuro */
  --border-color-hover: #444444; /* Gris medio */

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md:
    0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg:
    0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);

  /* Gradients adjusted for dark mode */
  --gradient-primary: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--secondary) 100%
  );
  --gradient-cta: linear-gradient(
    135deg,
    var(--accent) 0%,
    var(--secondary) 100%
  );
  --gradient-hero: linear-gradient(
    135deg,
    var(--primary-dark) 0%,
    var(--primary) 50%,
    var(--secondary) 100%
  );
  --gradient-card: linear-gradient(to bottom right, var(--gray-light), #1e293b);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-outfit);
  --font-mono: var(--font-geist-mono);
}

/* Removed media query for dark mode as we're using class-based dark mode */

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), 'Outfit', Arial, Helvetica, sans-serif;
}

/* Smooth scrolling for the entire site */
html {
  scroll-behavior: smooth;
}

/* Smooth theme transitions */
html,
body,
* {
  transition-property:
    background-color, border-color, color, fill, stroke, opacity, box-shadow,
    transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Disable transitions for specific elements that shouldn't animate */
.no-transition,
.no-transition * {
  transition: none !important;
}

/* Improved Typography with Outfit font */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-sans), 'Outfit', sans-serif;
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: -0.02em;
  margin-bottom: 1rem;
  text-align: center;
  border-radius: 0.5rem;
}

h1 {
  font-size: 3.5rem;
  font-weight: 700;
  letter-spacing: -0.03em;
  color: var(--primary);
}

h2 {
  font-size: 2.5rem;
  letter-spacing: -0.02em;
  color: var(--primary);
}

h3 {
  font-size: 2rem;
  letter-spacing: -0.01em;
}

h4 {
  font-size: 1.5rem;
}

/* Subtítulos con color secundario */
.subtitle {
  font-size: 1.25rem;
  color: var(--gray-medium);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  text-align: center;
}

p {
  font-family: var(--font-sans), 'Outfit', sans-serif;
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 1.1rem;
  }
}

/* Text gradient utility */
.text-gradient-primary {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.text-gradient-cta {
  background: var(--gradient-cta);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Estilo de texto tecnológico - Ver tech-effects.css para la implementación completa */

/* Animations */
/* Animaciones mejoradas con más impacto */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(40px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(40px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-40px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes bounce-subtle {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-slow {
  0%,
  100% {
    opacity: 0;
  }
  50% {
    opacity: 0.1;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Animation utility classes */
.animate-fade-in {
  animation: fadeIn var(--transition-normal) ease-in-out;
}

.animate-slide-up {
  animation: slideUp var(--transition-normal) ease-out;
}

.animate-slide-in-right {
  animation: slideInRight var(--transition-normal) ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft var(--transition-normal) ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-bounce-subtle {
  animation: bounce-subtle 3s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

/* Animaciones al hacer scroll */
.animate-in {
  opacity: 1 !important;
  transform: translateY(0) !important;
}

/* Hover animations - más pronunciadas */
.hover-lift {
  transition: transform var(--transition-normal) ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-8px);
}

.hover-shadow {
  transition: box-shadow var(--transition-normal) ease-in-out;
}

.hover-shadow:hover {
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.2),
    0 10px 10px -5px rgba(0, 0, 0, 0.1);
}

/* Additional animations */
@keyframes float-delay {
  0% {
    transform: translateY(-10px);
  }
  50% {
    transform: translateY(10px);
  }
  100% {
    transform: translateY(-10px);
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes spin-slow-reverse {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

@keyframes blob {
  0% {
    transform: scale(1);
  }
  33% {
    transform: scale(1.2);
  }
  66% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delay {
  animation: float-delay 8s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

.animate-spin-slow-reverse {
  animation: spin-slow-reverse 25s linear infinite;
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Grid background */
.bg-grid-white {
  background-image:
    linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 40px 40px;
}

.dark .bg-grid-white {
  background-image:
    linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 40px 40px;
}

.bg-grid-gray-200 {
  background-image:
    linear-gradient(to right, rgba(229, 231, 235, 0.5) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(229, 231, 235, 0.5) 1px, transparent 1px);
  background-size: 40px 40px;
}

.dark .bg-grid-gray-200 {
  background-image:
    linear-gradient(to right, rgba(71, 85, 105, 0.3) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(71, 85, 105, 0.3) 1px, transparent 1px);
  background-size: 40px 40px;
}

/* Código de líneas de fondo - Ver tech-effects.css para la implementación completa */

/* Efecto de escaneo - Ver tech-effects.css para la implementación completa */

/* Efecto de matriz */
.matrix-bg {
  position: relative;
}

.matrix-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%2300B4DB' fill-opacity='0.05' d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z'%3E%3C/path%3E%3C/svg%3E");
  pointer-events: none;
  z-index: 1;
}

/* Animaciones para la línea de tiempo y FAQs */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in-scale {
  animation: fadeInScale var(--transition-normal) ease-out;
}

@keyframes expandVertical {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 500px;
    opacity: 1;
  }
}

.animate-expand {
  animation: expandVertical var(--transition-normal) ease-out;
}

/* Animaciones para el CTA innovador */
@keyframes particle {
  0% {
    transform: translate(0, 0);
    opacity: 1;
  }
  100% {
    transform: translate(var(--x, 50px), var(--y, 50px));
    opacity: 0;
  }
}

.animate-particle {
  animation: particle 0.8s ease-out forwards;
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
}

.animate-glow {
  animation: glow 2s infinite;
}

@keyframes rotate3d {
  0% {
    transform: perspective(1000px) rotateX(0) rotateY(0);
  }
  50% {
    transform: perspective(1000px) rotateX(10deg) rotateY(10deg);
  }
  100% {
    transform: perspective(1000px) rotateX(0) rotateY(0);
  }
}

.animate-rotate-3d {
  animation: rotate3d 8s ease-in-out infinite;
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  100% {
    transform: scale(2.5);
    opacity: 0;
  }
}

/* Curva de aceleración suave para animaciones */
.ease-out-cubic {
  transition-timing-function: cubic-bezier(0.33, 1, 0.68, 1);
}

/* Animaciones de flotación - Ver neural-network.css para la implementación completa */
.animate-float-delay {
  animation: float 8s ease-in-out 1s infinite;
}

/* Animaciones de líneas para la página de casos de éxito */

/* Efecto de ripple para botones */
.ripple-effect {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.4);
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}
@keyframes draw-line {
  0% {
    stroke-dashoffset: 1000;
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    stroke-dashoffset: 0;
    opacity: 0.2;
  }
}

.animate-draw-line-1 {
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
  animation: draw-line 8s ease-in-out forwards;
}

.animate-draw-line-2 {
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
  animation: draw-line 8s ease-in-out 0.5s forwards;
}

.animate-draw-line-3 {
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
  animation: draw-line 8s ease-in-out 1s forwards;
}

.animate-draw-line-4 {
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
  animation: draw-line 8s ease-in-out 1.5s forwards;
}

/* Estilos para efectos 3D */
.perspective-1000 {
  perspective: 1000px;
}

/* Animación de brillo */
@keyframes shine {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-shine {
  animation: shine 3s linear infinite;
  background-size: 200% 100%;
}
