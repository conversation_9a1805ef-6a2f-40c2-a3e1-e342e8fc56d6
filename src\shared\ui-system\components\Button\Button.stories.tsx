import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'
import { ChevronRight, Download, Heart, Star, Zap } from 'lucide-react'

import Button from './Button'
import { ThemeProvider } from '@/context/ThemeContext'

const meta: Meta<typeof Button> = {
  title: 'Design System/Button',
  component: Button,
  decorators: [
    (Story) => (
      <ThemeProvider>
        <div className="p-4">
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'ATLAS v2.4 Unified Button Component. Consolidates all button functionality with design system tokens, multiple variants, effects, and full accessibility support.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'outline', 'accent', 'gradient', 'ghost'],
      description: 'Visual style variant of the button',
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg', 'xl'],
      description: 'Size of the button',
    },
    effect: {
      control: 'select',
      options: ['none', 'ripple', 'particles', 'glow'],
      description: 'Visual effect on interaction',
    },
    iconPosition: {
      control: 'select',
      options: ['left', 'right'],
      description: 'Position of the icon relative to text',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the button is disabled',
    },
    loading: {
      control: 'boolean',
      description: 'Whether the button is in loading state',
    },
    animate: {
      control: 'boolean',
      description: 'Whether to show hover/tap animations',
    },
    fullWidth: {
      control: 'boolean',
      description: 'Whether the button should take full width',
    },
    onClick: { action: 'clicked' },
  },
}

export default meta
type Story = StoryObj<typeof meta>

// Basic variants
export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Primary Button',
  },
}

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Secondary Button',
  },
}

export const Outline: Story = {
  args: {
    variant: 'outline',
    children: 'Outline Button',
  },
}

export const Accent: Story = {
  args: {
    variant: 'accent',
    children: 'Accent Button',
  },
}

export const Gradient: Story = {
  args: {
    variant: 'gradient',
    children: 'Gradient Button',
  },
}

export const Ghost: Story = {
  args: {
    variant: 'ghost',
    children: 'Ghost Button',
  },
}

// Size variations
export const Small: Story = {
  args: {
    size: 'sm',
    children: 'Small Button',
  },
}

export const Medium: Story = {
  args: {
    size: 'md',
    children: 'Medium Button',
  },
}

export const Large: Story = {
  args: {
    size: 'lg',
    children: 'Large Button',
  },
}

export const ExtraLarge: Story = {
  args: {
    size: 'xl',
    children: 'Extra Large Button',
  },
}

// State variations
export const Disabled: Story = {
  args: {
    disabled: true,
    children: 'Disabled Button',
  },
}

export const Loading: Story = {
  args: {
    loading: true,
    children: 'Loading Button',
  },
}

export const FullWidth: Story = {
  args: {
    fullWidth: true,
    children: 'Full Width Button',
  },
}

// Icon variations
export const WithIconRight: Story = {
  args: {
    icon: <ChevronRight size={16} />,
    iconPosition: 'right',
    children: 'Next Step',
  },
}

export const WithIconLeft: Story = {
  args: {
    icon: <Download size={16} />,
    iconPosition: 'left',
    children: 'Download',
  },
}

export const IconOnly: Story = {
  args: {
    icon: <Heart size={16} />,
    'aria-label': 'Like',
    children: '',
  },
}

// Effect variations
export const RippleEffect: Story = {
  args: {
    effect: 'ripple',
    children: 'Ripple Effect',
  },
}

export const ParticleEffect: Story = {
  args: {
    effect: 'particles',
    children: 'Particle Effect',
  },
}

export const GlowEffect: Story = {
  args: {
    effect: 'glow',
    children: 'Glow Effect',
  },
}

// Link variations
export const AsLink: Story = {
  args: {
    href: '/example',
    children: 'Internal Link',
    variant: 'primary',
  },
}

export const ExternalLink: Story = {
  args: {
    href: 'https://example.com',
    external: true,
    children: 'External Link',
    variant: 'outline',
    icon: <ChevronRight size={16} />,
  },
}

// Complex examples
export const CallToAction: Story = {
  args: {
    variant: 'gradient',
    size: 'lg',
    effect: 'glow',
    icon: <Zap size={20} />,
    iconPosition: 'left',
    children: 'Get Started Now',
  },
}

export const LoadingWithIcon: Story = {
  args: {
    loading: true,
    icon: <Star size={16} />,
    children: 'Processing...',
  },
}

// Showcase stories
export const AllVariants: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button variant="primary">Primary</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="outline">Outline</Button>
      <Button variant="accent">Accent</Button>
      <Button variant="gradient">Gradient</Button>
      <Button variant="ghost">Ghost</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'All available button variants displayed together.',
      },
    },
  },
}

export const AllSizes: Story = {
  render: () => (
    <div className="flex flex-wrap items-center gap-4">
      <Button size="sm">Small</Button>
      <Button size="md">Medium</Button>
      <Button size="lg">Large</Button>
      <Button size="xl">Extra Large</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'All available button sizes displayed together.',
      },
    },
  },
}

export const AllEffects: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button effect="none">No Effect</Button>
      <Button effect="ripple">Ripple</Button>
      <Button effect="particles">Particles</Button>
      <Button effect="glow">Glow</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'All available button effects. Click to see the effects in action.',
      },
    },
  },
}

export const ResponsiveShowcase: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <Button variant="primary" fullWidth>Mobile First</Button>
        <Button variant="secondary" fullWidth>Responsive</Button>
        <Button variant="outline" fullWidth>Design</Button>
      </div>
      <div className="flex flex-col sm:flex-row gap-4">
        <Button variant="accent" size="lg" className="flex-1">
          Flexible Layout
        </Button>
        <Button variant="gradient" size="lg" icon={<ChevronRight size={20} />}>
          Action
        </Button>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Responsive button layouts that adapt to different screen sizes.',
      },
    },
  },
}

// Dark theme showcase
export const DarkTheme: Story = {
  decorators: [
    (Story) => (
      <ThemeProvider>
        <div className="dark bg-gray-900 p-8 rounded-lg">
          <div className="space-y-4">
            <h3 className="text-white text-lg font-semibold mb-4">Dark Theme Buttons</h3>
            <Story />
          </div>
        </div>
      </ThemeProvider>
    ),
  ],
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button variant="primary">Primary</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="outline">Outline</Button>
      <Button variant="accent">Accent</Button>
      <Button variant="gradient">Gradient</Button>
      <Button variant="ghost">Ghost</Button>
    </div>
  ),
  parameters: {
    backgrounds: {
      default: 'dark',
    },
    docs: {
      description: {
        story: 'Button variants in dark theme mode.',
      },
    },
  },
}

// Interactive playground
export const Playground: Story = {
  args: {
    variant: 'primary',
    size: 'md',
    effect: 'ripple',
    children: 'Interactive Button',
    animate: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Interactive playground to test all button properties and combinations.',
      },
    },
  },
}
