'use client';

import React, { useRef, useMemo } from 'react';
import { motion, useInView } from 'framer-motion';
import Button from '@/components/ui/Button';
import SectionHeading from '@/components/ui/SectionHeading';
import { useTheme } from '@/context/ThemeContext';

// Types
interface Service {
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  color: string;
  features: string[];
}

interface ServiceIconProps {
  children: React.ReactNode;
  colorIndex: number;
  isDarkMode: boolean;
}

interface ServiceCardProps {
  service: Service;
  index: number;
  isDarkMode: boolean;
}

// Color theme configuration
const COLOR_THEMES = {
  teal: {
    light: { bg: 'from-teal-100 to-teal-200', border: 'border-teal-200', text: 'text-teal-600', button: 'border-teal-500 text-teal-700 hover:bg-teal-50' },
    dark: { bg: 'from-teal-500/30 to-cyan-500/30', border: 'border-teal-500/30', text: 'text-teal-400', button: 'border-teal-500/30 text-teal-300 hover:bg-teal-900/30' }
  },
  blue: {
    light: { bg: 'from-blue-100 to-blue-200', border: 'border-blue-200', text: 'text-blue-600', button: 'border-blue-500 text-blue-700 hover:bg-blue-50' },
    dark: { bg: 'from-blue-500/30 to-indigo-500/30', border: 'border-blue-500/30', text: 'text-blue-400', button: 'border-blue-500/30 text-blue-300 hover:bg-blue-900/30' }
  },
  purple: {
    light: { bg: 'from-purple-100 to-purple-200', border: 'border-purple-200', text: 'text-purple-600', button: 'border-purple-500 text-purple-700 hover:bg-purple-50' },
    dark: { bg: 'from-purple-500/30 to-pink-500/30', border: 'border-purple-500/30', text: 'text-purple-400', button: 'border-purple-500/30 text-purple-300 hover:bg-purple-900/30' }
  },
  indigo: {
    light: { bg: 'from-indigo-100 to-indigo-200', border: 'border-indigo-200', text: 'text-indigo-600', button: 'border-indigo-500 text-indigo-700 hover:bg-indigo-50' },
    dark: { bg: 'from-indigo-500/30 to-purple-500/30', border: 'border-indigo-500/30', text: 'text-indigo-400', button: 'border-indigo-500/30 text-indigo-300 hover:bg-indigo-900/30' }
  },
  cyan: {
    light: { bg: 'from-cyan-100 to-cyan-200', border: 'border-cyan-200', text: 'text-cyan-600', button: 'border-cyan-500 text-cyan-700 hover:bg-cyan-50' },
    dark: { bg: 'from-cyan-500/30 to-blue-500/30', border: 'border-cyan-500/30', text: 'text-cyan-400', button: 'border-cyan-500/30 text-cyan-300 hover:bg-cyan-900/30' }
  }
} as const;

// Services data configuration
const SERVICES: Service[] = [
  {
    title: 'Formación In Company',
    description: 'Programas de capacitación personalizados en IA y tecnologías emergentes, diseñados específicamente para las necesidades de tu empresa y equipo.',
    icon: (
      <path
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth={2}
        d='M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10'
      />
    ),
    href: '/services',
    color: 'teal',
    features: [
      'Programas adaptados a las necesidades específicas',
      'Formación práctica y aplicable',
      'Instructores expertos en IA y tecnologías emergentes',
      'Seguimiento y soporte post-formación',
    ],
  },
  {
    title: 'Asesoría Estratégica',
    description: 'Consultoría especializada para implementar soluciones tecnológicas y de IA adaptadas a los objetivos y necesidades específicas de tu negocio.',
    icon: (
      <path
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth={2}
        d='M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z'
      />
    ),
    href: '/services',
    color: 'blue',
    features: [
      'Análisis de necesidades tecnológicas',
      'Planificación estratégica de implementación',
      'Optimización de procesos existentes',
      'Medición de resultados e impacto',
    ],
  },
  {
    title: 'Desarrollo de Cursos',
    description: 'Creación de programas formativos completos y materiales educativos de alta calidad en tecnologías avanzadas e inteligencia artificial.',
    icon: (
      <path
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth={2}
        d='M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253'
      />
    ),
    href: '/services',
    color: 'purple',
    features: [
      'Contenido actualizado y relevante',
      'Materiales didácticos interactivos',
      'Adaptación a diferentes niveles',
      'Enfoque práctico y aplicable',
    ],
  },
  {
    title: 'Automatizaciones',
    description: 'Optimización de operaciones y reducción de costos mediante la automatización inteligente de procesos y tareas repetitivas.',
    icon: (
      <path
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth={2}
        d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
      />
    ),
    href: '/services',
    color: 'indigo',
    features: [
      'Flujos de trabajo automatizados',
      'Integración con sistemas existentes',
      'Reducción de tareas repetitivas',
      'Análisis de datos inteligente',
    ],
  },
  {
    title: 'Desarrollo a Medida',
    description: 'Creación de sitios web, aplicaciones y soluciones IT personalizadas que se adaptan perfectamente a las necesidades específicas de tu empresa.',
    icon: (
      <path
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth={2}
        d='M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4'
      />
    ),
    href: '/services',
    color: 'cyan',
    features: [
      'Diseño personalizado y único',
      'Experiencia de usuario intuitiva',
      'Optimización para dispositivos móviles',
      'Integración con sistemas existentes',
    ],
  },
];
// Animation variants
const ANIMATION_VARIANTS = {
  container: {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.15, delayChildren: 0.1 },
    },
  },
  card: {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: 'spring', stiffness: 100, damping: 12 },
    },
    hover: {
      y: -8,
      scale: 1.02,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 20,
        duration: 0.3,
      },
    },
    tap: {
      scale: 0.98,
      transition: { duration: 0.1 },
    },
  },
};

/**
 * Service Icon Component
 * Renders the icon for each service with proper theming
 */
const ServiceIcon: React.FC<ServiceIconProps> = ({ children, colorIndex, isDarkMode }) => {
  const colors = ['teal', 'blue', 'purple', 'indigo', 'cyan'] as const;
  const colorKey = colors[colorIndex % colors.length];
  const theme = COLOR_THEMES[colorKey];
  const currentTheme = isDarkMode ? theme.dark : theme.light;

  return (
    <div
      className={`flex-shrink-0 w-14 h-14 sm:w-16 sm:h-16 rounded-lg bg-gradient-to-br ${currentTheme.bg} ${currentTheme.border} flex items-center justify-center border mx-auto sm:mx-0 mb-3 sm:mb-0 transition-all duration-300 group-hover:scale-110`}
    >
      <svg
        className={`w-7 h-7 sm:w-8 sm:h-8 ${currentTheme.text}`}
        fill='none'
        stroke='currentColor'
        viewBox='0 0 24 24'
        xmlns='http://www.w3.org/2000/svg'
        aria-hidden="true"
      >
        {children}
      </svg>
    </div>
  );
};

/**
 * Service Card Component
 * Individual service card with improved accessibility and animations
 */
const ServiceCard: React.FC<ServiceCardProps> = ({ service, index, isDarkMode }) => {
  const colors = ['teal', 'blue', 'purple', 'indigo', 'cyan'] as const;
  const colorKey = colors[index % colors.length];
  const theme = COLOR_THEMES[colorKey];
  const currentTheme = isDarkMode ? theme.dark : theme.light;

  return (
    <motion.article
      variants={ANIMATION_VARIANTS.card}
      whileHover='hover'
      whileTap='tap'
      className={`${
        isDarkMode
          ? 'bg-gray-800/90 border-gray-700/50'
          : 'bg-white/95 border-gray-200/70'
      } p-5 sm:p-6 rounded-xl shadow-md border transition-all group relative overflow-hidden hover:shadow-xl`}
      role="article"
      aria-labelledby={`service-title-${index}`}
    >
      {/* Hover effect background */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      <div className='flex flex-col sm:flex-row sm:items-start gap-4 relative z-10'>
        <ServiceIcon colorIndex={index} isDarkMode={isDarkMode}>
          {service.icon}
        </ServiceIcon>

        <div className='w-full'>
          <h3
            id={`service-title-${index}`}
            className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2 text-center sm:text-left`}
          >
            {service.title}
          </h3>
          <p
            className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'} text-sm leading-relaxed text-center sm:text-left mb-4`}
          >
            {service.description}
          </p>
        </div>
      </div>

      <div className='mt-5 sm:mt-4 text-center sm:text-left'>
        <Button
          href={service.href}
          variant='outline'
          className={`text-sm px-5 py-2.5 rounded-full w-full sm:w-auto transition-all duration-300 ${currentTheme.button}`}
          aria-label={`Ver más detalles sobre ${service.title}`}
        >
          Ver más detalles
        </Button>
      </div>
    </motion.article>
  );
};

/**
 * Services Section Component
 * Main component that renders all services with improved performance and accessibility
 */
const ServicesSection: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const isInView = useInView(sectionRef, { once: true, margin: '-100px 0px' });
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  // Memoize services to prevent unnecessary re-renders
  const memoizedServices = useMemo(() => SERVICES, []);

  return (
    <section
      ref={sectionRef}
      id='servicios'
      className={`py-16 sm:py-20 md:py-28 relative overflow-hidden ${
        isDarkMode
          ? 'bg-gradient-to-b from-gray-950 via-gray-900 to-gray-800'
          : 'bg-gradient-to-b from-blue-50 via-blue-50/70 to-slate-100'
      }`}
      role="region"
      aria-labelledby="services-heading"
    >
      {/* Background pattern */}
      <div
        className={`absolute inset-0 bg-[url('/images/grid-pattern.svg')] bg-[length:40px_40px] ${
          isDarkMode ? 'opacity-[0.05]' : 'opacity-[0.1]'
        }`}
        aria-hidden="true"
      />

      <motion.div
        className='container mx-auto px-4 sm:px-6 lg:px-8 relative z-10'
        initial='hidden'
        animate={isInView ? 'visible' : 'hidden'}
        variants={ANIMATION_VARIANTS.container}
      >
        {/* Section Header */}
        <motion.div variants={ANIMATION_VARIANTS.card}>
          <SectionHeading
            title={
              <span
                id="services-heading"
                className='text-transparent bg-clip-text bg-gradient-to-r from-[#00B4DB] via-[#48D1CC] to-[#00BFFF] font-bold'
              >
                Nuestros Servicios
              </span>
            }
            subtitle={
              <span
                className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'} max-w-2xl mx-auto text-base md:text-lg`}
              >
                Soluciones prácticas que puedes explorar al detalle
              </span>
            }
            centered
            className='mb-8 sm:mb-12'
          />
        </motion.div>

        {/* Services Grid */}
        <motion.div
          className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-6 md:gap-8'
          variants={ANIMATION_VARIANTS.container}
          role="list"
          aria-label="Lista de servicios"
        >
          {memoizedServices.map((service, index) => (
            <ServiceCard
              key={`${service.title}-${index}`}
              service={service}
              index={index}
              isDarkMode={isDarkMode}
            />
          ))}
        </motion.div>
      </motion.div>
    </section>
  );
};

export default ServicesSection;
