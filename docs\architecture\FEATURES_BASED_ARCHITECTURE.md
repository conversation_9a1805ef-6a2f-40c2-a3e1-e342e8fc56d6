# Arquitectura Features-based ATLAS v2.4

## Visión General

La arquitectura features-based es un patrón de organización de código que agrupa funcionalidades relacionadas en módulos cohesivos y auto-contenidos, promoviendo la escalabilidad, mantenibilidad y reutilización.

## 🎯 Principios Fundamentales

### 1. Cohesión Alta
Cada feature contiene todo lo necesario para su funcionamiento:
- Componentes específicos
- Hooks personalizados
- Tipos y interfaces
- Tests unitarios
- Documentación

### 2. Acoplamiento Bajo
Las features son independientes entre sí:
- No dependen de implementaciones internas de otras features
- Comunican a través de interfaces bien definidas
- Pueden desarrollarse y desplegarse independientemente

### 3. Responsabilidad Única
Cada feature tiene una responsabilidad clara:
- Una funcionalidad de negocio específica
- Un dominio de problema bien definido
- Límites claros de responsabilidad

## 🏗️ Estructura de Directorios

```
src/
├── features/                    # Features principales
│   ├── contact/                # Feature de contacto
│   │   ├── components/         # Componentes específicos
│   │   │   ├── ContactHero/    # Componente hero
│   │   │   ├── ContactForm/    # Formulario de contacto
│   │   │   ├── ContactInfo/    # Información de contacto
│   │   │   ├── NeuralBackground/ # Fondo animado
│   │   │   ├── FormField/      # Campo de formulario
│   │   │   └── FormStatus/     # Estado del formulario
│   │   ├── hooks/              # Hooks específicos
│   │   │   ├── useContactForm.ts
│   │   │   └── useNeuralAnimation.ts
│   │   ├── types/              # Tipos específicos
│   │   │   └── contact.types.ts
│   │   ├── __tests__/          # Tests de la feature
│   │   │   ├── ContactPage.test.tsx
│   │   │   └── useContactForm.test.ts
│   │   ├── ContactPage.tsx     # Página principal
│   │   └── index.ts            # Exports públicos
│   │
│   ├── about/                  # Feature sobre nosotros
│   │   ├── components/
│   │   │   ├── AboutHero/
│   │   │   ├── CompanyHistory/
│   │   │   ├── MissionVision/
│   │   │   └── TeamSection/
│   │   ├── AboutPage.tsx
│   │   └── index.ts
│   │
│   └── home/                   # Feature página principal
│       ├── components/
│       │   └── HeroSection/
│       ├── hooks/
│       └── index.ts
│
├── shared/                     # Recursos compartidos
│   ├── ui-system/             # Design system
│   ├── components/            # Componentes globales
│   ├── hooks/                 # Hooks globales
│   ├── utils/                 # Utilidades
│   └── types/                 # Tipos globales
│
└── app/                       # App Router (Next.js)
    ├── contact/
    │   └── page.tsx           # Usa ContactPage feature
    ├── about/
    │   └── page.tsx           # Usa AboutPage feature
    └── page.tsx               # Usa Home feature
```

## 📦 Anatomía de una Feature

### Estructura Estándar

```typescript
// features/example/index.ts
export { default as ExamplePage } from './ExamplePage'
export { useExampleLogic } from './hooks/useExampleLogic'
export type { ExampleData, ExampleConfig } from './types/example.types'

// Solo exportar lo que debe ser público
```

### Componente Principal

```typescript
// features/example/ExamplePage.tsx
import { ExampleHero } from './components/ExampleHero'
import { ExampleContent } from './components/ExampleContent'
import { ExampleFooter } from './components/ExampleFooter'

/**
 * Página principal de la feature Example
 * 
 * Responsabilidades:
 * - Orquestar componentes de la feature
 * - Manejar estado global de la feature
 * - Proporcionar contexto si es necesario
 */
const ExamplePage: React.FC = () => {
  return (
    <div className="example-page">
      <ExampleHero />
      <ExampleContent />
      <ExampleFooter />
    </div>
  )
}

export default ExamplePage
```

### Componentes Específicos

```typescript
// features/example/components/ExampleHero/ExampleHero.tsx
import { useExampleLogic } from '../../hooks/useExampleLogic'
import { Button } from '@/shared/ui-system'

/**
 * Componente Hero específico de la feature Example
 * 
 * Características:
 * - <200 líneas de código
 * - Responsabilidad única
 * - Usa hooks de la feature
 * - Usa componentes del design system
 */
export const ExampleHero: React.FC = () => {
  const { data, actions } = useExampleLogic()
  
  return (
    <section className="example-hero">
      <h1>{data.title}</h1>
      <p>{data.description}</p>
      <Button onClick={actions.handleCTA}>
        {data.ctaText}
      </Button>
    </section>
  )
}
```

### Hooks Especializados

```typescript
// features/example/hooks/useExampleLogic.ts
import { useState, useCallback } from 'react'
import { ExampleData } from '../types/example.types'

/**
 * Hook para lógica específica de la feature Example
 * 
 * Responsabilidades:
 * - Manejar estado específico de la feature
 * - Encapsular lógica de negocio
 * - Proporcionar API consistente
 */
export const useExampleLogic = () => {
  const [data, setData] = useState<ExampleData>({
    title: 'Example Title',
    description: 'Example description',
    ctaText: 'Click me'
  })
  
  const handleCTA = useCallback(() => {
    // Lógica específica del CTA
  }, [])
  
  const actions = {
    handleCTA,
    updateData: setData
  }
  
  return { data, actions }
}
```

## 🔄 Patrones de Comunicación

### 1. Props Drilling (Evitar)

```typescript
// ❌ Malo: Props drilling
<ParentComponent data={data}>
  <ChildComponent data={data}>
    <GrandChildComponent data={data} />
  </ChildComponent>
</ParentComponent>
```

### 2. Context API (Para estado de feature)

```typescript
// ✅ Bueno: Context para estado de feature
const ExampleContext = createContext<ExampleContextType>()

export const ExampleProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const exampleLogic = useExampleLogic()
  
  return (
    <ExampleContext.Provider value={exampleLogic}>
      {children}
    </ExampleContext.Provider>
  )
}

export const useExampleContext = () => {
  const context = useContext(ExampleContext)
  if (!context) {
    throw new Error('useExampleContext must be used within ExampleProvider')
  }
  return context
}
```

### 3. Custom Hooks (Para lógica reutilizable)

```typescript
// ✅ Bueno: Custom hooks para lógica específica
export const useExampleForm = () => {
  const [formData, setFormData] = useState(initialFormData)
  const [errors, setErrors] = useState({})
  
  const validate = useCallback((data) => {
    // Lógica de validación específica
  }, [])
  
  const submit = useCallback(async (data) => {
    // Lógica de envío específica
  }, [])
  
  return { formData, errors, validate, submit }
}
```

## 🧪 Testing de Features

### Estructura de Tests

```typescript
// features/example/__tests__/ExamplePage.test.tsx
import { render, screen } from '@testing-library/react'
import { ExamplePage } from '../ExamplePage'
import { TestWrapper } from '@/shared/test-utils'

describe('ExamplePage Feature', () => {
  describe('Page Structure', () => {
    it('renders all main sections', () => {
      render(
        <TestWrapper>
          <ExamplePage />
        </TestWrapper>
      )
      
      expect(screen.getByTestId('example-hero')).toBeInTheDocument()
      expect(screen.getByTestId('example-content')).toBeInTheDocument()
      expect(screen.getByTestId('example-footer')).toBeInTheDocument()
    })
  })
  
  describe('Feature Logic', () => {
    it('handles user interactions correctly', () => {
      // Tests de lógica específica de la feature
    })
  })
  
  describe('Integration', () => {
    it('integrates with shared components', () => {
      // Tests de integración con design system
    })
  })
})
```

### Tests de Hooks

```typescript
// features/example/__tests__/useExampleLogic.test.ts
import { renderHook, act } from '@testing-library/react'
import { useExampleLogic } from '../hooks/useExampleLogic'

describe('useExampleLogic Hook', () => {
  it('initializes with correct default state', () => {
    const { result } = renderHook(() => useExampleLogic())
    
    expect(result.current.data.title).toBe('Example Title')
    expect(result.current.actions.handleCTA).toBeInstanceOf(Function)
  })
  
  it('handles CTA action correctly', () => {
    const { result } = renderHook(() => useExampleLogic())
    
    act(() => {
      result.current.actions.handleCTA()
    })
    
    // Verificar comportamiento esperado
  })
})
```

## 📊 Métricas y Monitoreo

### Métricas por Feature

```typescript
// features/example/metrics/example.metrics.ts
export const exampleMetrics = {
  // Métricas de performance
  performance: {
    loadTime: 'time to interactive',
    renderTime: 'component render time',
    bundleSize: 'feature bundle size'
  },
  
  // Métricas de uso
  usage: {
    pageViews: 'example page views',
    interactions: 'user interactions',
    conversions: 'goal completions'
  },
  
  // Métricas de calidad
  quality: {
    testCoverage: 'test coverage percentage',
    bugCount: 'open bugs',
    performanceScore: 'lighthouse score'
  }
}
```

### Monitoreo Automático

```typescript
// features/example/monitoring/example.monitoring.ts
export const setupExampleMonitoring = () => {
  // Performance monitoring
  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      if (entry.name.includes('example')) {
        // Enviar métricas a servicio de monitoreo
      }
    })
  })
  
  observer.observe({ entryTypes: ['measure', 'navigation'] })
}
```

## 🔧 Herramientas y Automatización

### Generador de Features

```bash
# Crear nueva feature
npm run generate:feature -- example

# Crear componente en feature
npm run generate:component -- example/ExampleCard

# Crear hook en feature
npm run generate:hook -- example/useExampleData
```

### Validación de Arquitectura

```typescript
// scripts/validate-features.js
const validateFeatureStructure = (featurePath) => {
  const requiredFiles = [
    'index.ts',
    'components/',
    '__tests__/'
  ]
  
  const optionalFiles = [
    'hooks/',
    'types/',
    'utils/',
    'constants/'
  ]
  
  return requiredFiles.every(file => 
    fs.existsSync(path.join(featurePath, file))
  )
}
```

## 📚 Best Practices

### 1. Naming Conventions

```typescript
// ✅ Bueno: Nombres descriptivos y consistentes
features/
├── user-profile/           # kebab-case para directorios
│   ├── UserProfilePage.tsx # PascalCase para componentes
│   ├── useUserProfile.ts   # camelCase para hooks
│   └── user-profile.types.ts # kebab-case para archivos de tipos
```

### 2. Límites de Tamaño

```typescript
// ✅ Bueno: Componentes pequeños y enfocados
// Máximo 200 líneas por componente
// Máximo 100 líneas por hook
// Máximo 50 líneas por utility function
```

### 3. Dependencias

```typescript
// ✅ Bueno: Dependencias claras y mínimas
import { Button } from '@/shared/ui-system'        // Design system
import { useApi } from '@/shared/hooks'            // Shared hooks
import { formatDate } from '@/shared/utils'        // Shared utils

// ❌ Malo: Dependencias entre features
import { useContactForm } from '@/features/contact' // No hacer esto
```

### 4. Exports

```typescript
// ✅ Bueno: Exports explícitos y controlados
// features/example/index.ts
export { default as ExamplePage } from './ExamplePage'
export { useExampleLogic } from './hooks/useExampleLogic'
export type { ExampleData } from './types/example.types'

// No exportar componentes internos
// No exportar utilidades internas
// No exportar constantes internas
```

## 🚀 Beneficios Obtenidos

### Desarrollo

- **Velocidad**: Desarrollo paralelo de features
- **Claridad**: Responsabilidades bien definidas
- **Reutilización**: Componentes y hooks específicos
- **Testing**: Tests enfocados y específicos

### Mantenimiento

- **Localización**: Cambios localizados en features
- **Debugging**: Problemas fáciles de localizar
- **Refactoring**: Refactoring seguro y controlado
- **Escalabilidad**: Crecimiento orgánico del proyecto

### Calidad

- **Consistencia**: Patrones consistentes en todas las features
- **Performance**: Optimización específica por feature
- **Accesibilidad**: Validación por feature
- **Documentation**: Documentación específica y detallada

---

**Arquitectura Features-based ATLAS v2.4** - Construyendo aplicaciones escalables, mantenibles y de alta calidad con organización modular y responsabilidades claras.
