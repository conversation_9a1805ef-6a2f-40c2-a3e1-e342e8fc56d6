/**
 * ATLAS v2.4 - Production Configuration
 * Optimized configuration for production deployment
 */

// Environment-specific configurations
const productionConfig = {
  // Performance optimizations
  performance: {
    // Bundle optimization
    bundleOptimization: {
      minify: true,
      compress: true,
      treeshake: true,
      splitChunks: true,
      codeElimination: true
    },
    
    // Caching strategies
    caching: {
      staticAssets: '1y',        // 1 year for static assets
      dynamicContent: '1h',      // 1 hour for dynamic content
      apiResponses: '5m',        // 5 minutes for API responses
      serviceWorker: '0',        // No cache for service worker
    },
    
    // Compression
    compression: {
      gzip: true,
      brotli: true,
      level: 9,                  // Maximum compression
      threshold: 1024,           // Compress files > 1KB
    },
    
    // Image optimization
    images: {
      formats: ['webp', 'avif', 'jpeg'],
      quality: 85,
      progressive: true,
      optimization: true,
      responsive: true,
    }
  },
  
  // Security configurations
  security: {
    // Headers
    headers: {
      'X-Frame-Options': 'DENY',
      'X-Content-Type-Options': 'nosniff',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'Content-Security-Policy': `
        default-src 'self';
        script-src 'self' 'unsafe-inline' 'unsafe-eval' https://api.emailjs.com;
        style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
        font-src 'self' https://fonts.gstatic.com;
        img-src 'self' data: https:;
        connect-src 'self' https://api.emailjs.com;
        frame-ancestors 'none';
        base-uri 'self';
        form-action 'self';
      `.replace(/\s+/g, ' ').trim()
    },
    
    // CORS
    cors: {
      origin: process.env.ALLOWED_ORIGINS?.split(',') || ['https://informatik-ai.com'],
      credentials: true,
      optionsSuccessStatus: 200
    }
  },
  
  // Monitoring and analytics
  monitoring: {
    // Performance monitoring
    performance: {
      enabled: true,
      sampleRate: 1.0,          // Monitor 100% of requests
      vitals: ['LCP', 'FID', 'CLS', 'FCP', 'TTI'],
      thresholds: {
        LCP: 2500,              // ms
        FID: 100,               // ms
        CLS: 0.1,               // score
        FCP: 1800,              // ms
        TTI: 3800,              // ms
      }
    },
    
    // Error tracking
    errorTracking: {
      enabled: true,
      sampleRate: 1.0,
      ignoreErrors: [
        'ResizeObserver loop limit exceeded',
        'Non-Error promise rejection captured'
      ]
    },
    
    // Analytics
    analytics: {
      enabled: true,
      trackPageViews: true,
      trackUserInteractions: true,
      trackPerformance: true,
      trackErrors: true
    }
  },
  
  // SEO optimizations
  seo: {
    // Meta tags
    meta: {
      title: 'InformatiK-AI - Soluciones de Inteligencia Artificial',
      description: 'Transformamos empresas a través del poder de la inteligencia artificial. Servicios de IA, formación, consultoría y desarrollo personalizado.',
      keywords: 'inteligencia artificial, IA, machine learning, consultoría, formación, desarrollo',
      author: 'InformatiK-AI Team',
      robots: 'index, follow',
      canonical: 'https://informatik-ai.com'
    },
    
    // Open Graph
    openGraph: {
      type: 'website',
      locale: 'es_ES',
      siteName: 'InformatiK-AI',
      title: 'InformatiK-AI - Soluciones de Inteligencia Artificial',
      description: 'Transformamos empresas a través del poder de la inteligencia artificial.',
      image: 'https://informatik-ai.com/images/og-image.jpg',
      url: 'https://informatik-ai.com'
    },
    
    // Structured data
    structuredData: {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: 'InformatiK-AI',
      description: 'Soluciones de Inteligencia Artificial',
      url: 'https://informatik-ai.com',
      logo: 'https://informatik-ai.com/images/logo.png',
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: '+34-XXX-XXX-XXX',
        contactType: 'customer service',
        availableLanguage: 'Spanish'
      }
    }
  },
  
  // PWA configuration
  pwa: {
    enabled: true,
    manifest: {
      name: 'InformatiK-AI',
      shortName: 'InformatiK-AI',
      description: 'Soluciones de Inteligencia Artificial',
      startUrl: '/',
      display: 'standalone',
      backgroundColor: '#000000',
      themeColor: '#00B4DB',
      orientation: 'portrait-primary'
    },
    
    // Service worker
    serviceWorker: {
      enabled: true,
      scope: '/',
      updateViaCache: 'none',
      strategies: {
        pages: 'NetworkFirst',
        images: 'CacheFirst',
        static: 'CacheFirst',
        api: 'NetworkFirst'
      }
    },
    
    // Offline support
    offline: {
      enabled: true,
      fallbackPage: '/offline',
      precachePages: ['/', '/about', '/contact', '/services']
    }
  },
  
  // Build optimizations
  build: {
    // Source maps
    sourceMaps: false,          // Disable in production for security
    
    // Minification
    minification: {
      removeComments: true,
      removeConsole: true,
      removeDebugger: true,
      mangleProps: true
    },
    
    // Bundle analysis
    analyze: process.env.ANALYZE === 'true',
    
    // Output optimization
    output: {
      clean: true,
      hashDigestLength: 8,
      publicPath: '/_next/',
      assetModuleFilename: 'static/[hash][ext]'
    }
  },
  
  // Runtime configuration
  runtime: {
    // Node.js optimizations
    nodeOptions: [
      '--max-old-space-size=4096',
      '--optimize-for-size'
    ],
    
    // Environment variables
    env: {
      NODE_ENV: 'production',
      NEXT_TELEMETRY_DISABLED: '1',
      GENERATE_SOURCEMAP: 'false'
    }
  }
}

// Vercel-specific configuration
const vercelConfig = {
  // Build settings
  buildCommand: 'npm run build',
  outputDirectory: '.next',
  installCommand: 'npm ci',
  
  // Functions
  functions: {
    'pages/api/**/*.js': {
      runtime: 'nodejs18.x',
      maxDuration: 10
    }
  },
  
  // Headers
  headers: [
    {
      source: '/(.*)',
      headers: Object.entries(productionConfig.security.headers).map(([key, value]) => ({
        key,
        value
      }))
    },
    {
      source: '/static/(.*)',
      headers: [
        {
          key: 'Cache-Control',
          value: 'public, max-age=31536000, immutable'
        }
      ]
    },
    {
      source: '/_next/static/(.*)',
      headers: [
        {
          key: 'Cache-Control',
          value: 'public, max-age=31536000, immutable'
        }
      ]
    }
  ],
  
  // Redirects
  redirects: [
    // Add any necessary redirects here
  ],
  
  // Rewrites
  rewrites: [
    // Add any necessary rewrites here
  ]
}

// Netlify-specific configuration
const netlifyConfig = {
  // Build settings
  build: {
    command: 'npm run build && npm run export',
    publish: 'out',
    environment: productionConfig.runtime.env
  },
  
  // Headers
  headers: [
    {
      for: '/*',
      values: productionConfig.security.headers
    },
    {
      for: '/static/*',
      values: {
        'Cache-Control': 'public, max-age=31536000, immutable'
      }
    }
  ],
  
  // Redirects
  redirects: [
    // Add any necessary redirects here
  ]
}

// Export configurations
module.exports = {
  production: productionConfig,
  vercel: vercelConfig,
  netlify: netlifyConfig,
  
  // Helper function to get config by platform
  getConfig: (platform = 'production') => {
    switch (platform) {
      case 'vercel':
        return { ...productionConfig, deployment: vercelConfig }
      case 'netlify':
        return { ...productionConfig, deployment: netlifyConfig }
      default:
        return productionConfig
    }
  }
}
