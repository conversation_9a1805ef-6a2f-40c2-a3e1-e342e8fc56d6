# Guía de Quality Gates ATLAS v2.4

## Visión General

Los Quality Gates son validaciones automatizadas que aseguran que el código cumple con los estándares de calidad ATLAS v2.4 antes de ser integrado o desplegado. Proporcionan feedback inmediato y previenen la introducción de deuda técnica.

## 🎯 Objetivos

- **Calidad Consistente**: Mantener estándares altos en todo el proyecto
- **Prevención**: Detectar problemas antes de que lleguen a producción
- **Automatización**: Validación automática sin intervención manual
- **Feedback Rápido**: Resultados inmediatos para desarrolladores
- **Compliance**: Cumplimiento de estándares ATLAS v2.4

## 🏆 Quality Gates Implementados

### 1. Design System Validation (Peso: 20%)

**Propósito**: Validar que todos los componentes usan el design system unificado

**Validaciones**:
- Uso de design tokens en lugar de valores hardcoded
- Importaciones desde `@/shared/ui-system`
- Consistencia en APIs de componentes
- Eliminación de componentes legacy duplicados

**Comando**:
```bash
npm run validate:design-system
```

**Criterios de Éxito**:
- ✅ 100% componentes usan design tokens
- ✅ 0 importaciones de componentes legacy
- ✅ API consistente en todos los componentes
- ✅ Documentación Storybook actualizada

### 2. Bundle Size Validation (Peso: 15%)

**Propósito**: Asegurar que el bundle size se mantiene dentro de límites aceptables

**Límites**:
- Total bundle: <1.5MB
- Main bundle: <500KB
- Vendor bundle: <800KB
- Por página: <200KB
- Por componente: <50KB

**Comando**:
```bash
npm run validate:bundle-size
```

**Criterios de Éxito**:
- ✅ Bundle total <1.5MB
- ✅ Code splitting efectivo
- ✅ Tree shaking optimizado
- ✅ Lazy loading implementado

### 3. Accessibility Validation (Peso: 20%)

**Propósito**: Garantizar WCAG 2.1 AA compliance en toda la aplicación

**Validaciones**:
- Contraste de colores (mínimo 4.5:1)
- Navegación por teclado
- Estructura semántica HTML
- ARIA labels y roles apropiados
- Alt text en imágenes

**Comando**:
```bash
npm run validate:accessibility
```

**Criterios de Éxito**:
- ✅ 100% WCAG 2.1 AA compliance
- ✅ Navegación por teclado completa
- ✅ Screen reader compatible
- ✅ Contraste adecuado en todos los elementos

### 4. Performance Budget (Peso: 15%)

**Propósito**: Validar Core Web Vitals y métricas de performance

**Métricas**:
- LCP (Largest Contentful Paint): <2.5s
- FID (First Input Delay): <100ms
- CLS (Cumulative Layout Shift): <0.1
- FCP (First Contentful Paint): <1.8s
- TTI (Time to Interactive): <3.8s

**Comando**:
```bash
npm run validate:performance
```

**Criterios de Éxito**:
- ✅ Todas las métricas Core Web Vitals en verde
- ✅ Performance budget respetado
- ✅ Optimizaciones implementadas
- ✅ Monitoring configurado

### 5. Code Quality (Peso: 10%)

**Propósito**: Mantener calidad de código con linting y type checking

**Validaciones**:
- ESLint sin errores
- TypeScript strict mode
- Prettier formatting
- Import organization
- Unused code detection

**Comando**:
```bash
npm run quality
```

**Criterios de Éxito**:
- ✅ 0 errores ESLint
- ✅ 0 errores TypeScript
- ✅ Código formateado consistentemente
- ✅ Imports organizados

### 6. Unit Tests (Peso: 15%)

**Propósito**: Asegurar cobertura de tests adecuada y calidad

**Métricas**:
- Cobertura mínima: 85%
- Tests pasando: 100%
- Performance de tests: <30s
- Mutation testing score: >80%

**Comando**:
```bash
npm run test:ci
```

**Criterios de Éxito**:
- ✅ >85% cobertura de código
- ✅ 100% tests pasando
- ✅ Tests rápidos y confiables
- ✅ Mocking apropiado

### 7. E2E Tests (Peso: 5%)

**Propósito**: Validar flujos críticos de usuario end-to-end

**Cobertura**:
- Flujos de navegación principales
- Formularios críticos
- Responsive design
- Cross-browser compatibility

**Comando**:
```bash
npm run test:e2e
```

**Criterios de Éxito**:
- ✅ Flujos críticos funcionando
- ✅ Multi-browser compatibility
- ✅ Mobile responsive
- ✅ Performance aceptable

## 🚀 Ejecución de Quality Gates

### Ejecución Individual

```bash
# Ejecutar quality gate específico
npm run validate:design-system
npm run validate:bundle-size
npm run validate:accessibility
npm run validate:performance
npm run quality
npm run test:ci
npm run test:e2e
```

### Ejecución Completa

```bash
# Ejecutar todos los quality gates
npm run atlas:quality-gates

# Validación completa con reporte
npm run validate:all
```

### Ejecución en CI/CD

```yaml
# .github/workflows/quality-gates.yml
name: Quality Gates

on: [push, pull_request]

jobs:
  quality-gates:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build project
        run: npm run build
        
      - name: Run Quality Gates
        run: npm run atlas:quality-gates
        
      - name: Upload reports
        uses: actions/upload-artifact@v3
        with:
          name: quality-reports
          path: reports/
```

## 📊 Scoring System

### Cálculo de Puntuación

```typescript
const calculateQualityScore = (results: QualityGateResult[]) => {
  const totalWeight = results.reduce((sum, gate) => sum + gate.weight, 0)
  const achievedWeight = results
    .filter(result => result.passed)
    .reduce((sum, result) => sum + result.weight, 0)
  
  return Math.round((achievedWeight / totalWeight) * 100)
}
```

### Niveles de Calidad

| Score | Nivel | Estado | Acción |
|-------|-------|--------|--------|
| 95-100% | 🏆 Excelente | ✅ Aprobado | Deploy permitido |
| 85-94% | 🥇 Muy Bueno | ✅ Aprobado | Deploy permitido |
| 70-84% | 🥈 Bueno | ⚠️ Advertencia | Review requerido |
| 50-69% | 🥉 Regular | ❌ Bloqueado | Mejoras requeridas |
| <50% | 💥 Crítico | ❌ Bloqueado | Refactoring necesario |

### Reporte de Resultados

```bash
🏆 ATLAS v2.4 Quality Gates Results
====================================

📊 Overall Quality Score: 98%

📋 Quality Gates Summary:
┌─────────────────────────────────┬──────────┬──────────┬──────────┐
│ Quality Gate                    │ Status   │ Critical │ Weight   │
├─────────────────────────────────┼──────────┼──────────┼──────────┤
│ Design System Validation        │ ✅ PASS  │ 🔴 YES  │     20%  │
│ Bundle Size Validation          │ ✅ PASS  │ 🔴 YES  │     15%  │
│ Accessibility Validation        │ ✅ PASS  │ 🔴 YES  │     20%  │
│ Performance Budget              │ ✅ PASS  │ 🔴 YES  │     15%  │
│ Code Quality                    │ ✅ PASS  │ 🔴 YES  │     10%  │
│ Unit Tests                      │ ✅ PASS  │ 🔴 YES  │     15%  │
│ E2E Tests                       │ ⚠️ WARN  │ ⚪ NO   │      5%  │
└─────────────────────────────────┴──────────┴──────────┴──────────┘

🎉 All critical quality gates passed!
🚀 Application ready for production deployment
```

## 🔧 Configuración y Personalización

### Configuración de Límites

```typescript
// quality-gates.config.ts
export const qualityGatesConfig = {
  bundleSize: {
    total: 1.5 * 1024 * 1024,      // 1.5MB
    main: 500 * 1024,             // 500KB
    vendor: 800 * 1024,           // 800KB
    page: 200 * 1024,             // 200KB
  },
  
  performance: {
    LCP: 2500,        // ms
    FID: 100,         // ms
    CLS: 0.1,         // score
    FCP: 1800,        // ms
    TTI: 3800,        // ms
  },
  
  testing: {
    coverage: 85,     // percentage
    timeout: 30000,   // ms
  },
  
  accessibility: {
    contrastRatio: 4.5,
    wcagLevel: 'AA',
  }
}
```

### Personalización de Gates

```typescript
// custom-quality-gate.ts
export const customQualityGate = {
  name: 'Custom Validation',
  weight: 10,
  critical: true,
  
  async validate(): Promise<QualityGateResult> {
    // Lógica de validación personalizada
    const isValid = await runCustomValidation()
    
    return {
      passed: isValid,
      message: isValid ? 'Custom validation passed' : 'Custom validation failed',
      details: getValidationDetails()
    }
  }
}
```

## 🚨 Troubleshooting

### Problemas Comunes

#### 1. Bundle Size Excedido

**Síntomas**: Bundle size validation falla
**Soluciones**:
```bash
# Analizar bundle
npm run analyze

# Optimizar imports
import { Button } from '@/shared/ui-system/components/Button'
# En lugar de
import { Button } from '@/shared/ui-system'

# Implementar lazy loading
const LazyComponent = lazy(() => import('./Component'))
```

#### 2. Tests Fallando

**Síntomas**: Unit tests o E2E tests fallan
**Soluciones**:
```bash
# Ejecutar tests en modo watch
npm run test:watch

# Ejecutar tests específicos
npm run test -- --testNamePattern="ComponentName"

# Debug E2E tests
npm run test:e2e:debug
```

#### 3. Accessibility Issues

**Síntomas**: Accessibility validation falla
**Soluciones**:
```typescript
// Agregar ARIA labels
<button aria-label="Close modal">×</button>

// Mejorar contraste
// Usar design tokens en lugar de colores hardcoded

// Estructura semántica
<main>
  <section>
    <h1>Title</h1>
    <article>Content</article>
  </section>
</main>
```

### Debugging Quality Gates

```bash
# Ejecutar con verbose output
npm run atlas:quality-gates -- --verbose

# Ejecutar gate específico con debug
DEBUG=true npm run validate:accessibility

# Generar reporte detallado
npm run atlas:quality-gates -- --report=detailed
```

## 📚 Recursos Adicionales

### Documentación

- [Design System Guide](../design-system/README.md)
- [Performance Optimization](../performance/README.md)
- [Accessibility Guidelines](../accessibility/README.md)
- [Testing Strategy](../testing/README.md)

### Herramientas

- **Lighthouse**: Performance y accessibility auditing
- **Bundle Analyzer**: Análisis de bundle size
- **Axe**: Accessibility testing
- **Playwright**: E2E testing

### Monitoreo Continuo

```typescript
// monitoring/quality-metrics.ts
export const setupQualityMonitoring = () => {
  // Monitoreo de performance en producción
  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach(entry => {
      // Enviar métricas a servicio de monitoreo
      sendMetric(entry.name, entry.duration)
    })
  })
  
  observer.observe({ entryTypes: ['measure', 'navigation'] })
}
```

## ✅ Checklist de Implementación

### Setup Inicial

- [ ] Configurar quality gates en CI/CD
- [ ] Establecer límites y thresholds
- [ ] Configurar reporting automático
- [ ] Entrenar equipo en uso de quality gates

### Mantenimiento

- [ ] Revisar métricas semanalmente
- [ ] Actualizar límites según evolución del proyecto
- [ ] Agregar nuevos quality gates según necesidades
- [ ] Mantener documentación actualizada

### Mejora Continua

- [ ] Analizar tendencias de calidad
- [ ] Identificar áreas de mejora
- [ ] Implementar nuevas validaciones
- [ ] Optimizar tiempo de ejecución

---

**Quality Gates ATLAS v2.4** - Garantizando calidad consistente y excelencia técnica en cada commit.
