import { renderHook, act } from '@testing-library/react'
import { useContactForm } from '../hooks/useContactForm'

// Mock EmailJS
jest.mock('@emailjs/browser', () => ({
  init: jest.fn(),
  sendForm: jest.fn(() => Promise.resolve({ status: 200, text: 'OK' })),
}))

describe('useContactForm Hook - ATLAS v2.4', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Initial State', () => {
    it('initializes with empty form data', () => {
      const { result } = renderHook(() => useContactForm())
      
      expect(result.current.formData).toEqual({
        name: '',
        email: '',
        company: '',
        phone: '',
        message: '',
        service: '',
      })
    })

    it('initializes with no status message', () => {
      const { result } = renderHook(() => useContactForm())
      
      expect(result.current.formStatus).toEqual({
        type: null,
        message: '',
      })
    })

    it('initializes as not submitting', () => {
      const { result } = renderHook(() => useContactForm())
      
      expect(result.current.isSubmitting).toBe(false)
    })

    it('provides form reference', () => {
      const { result } = renderHook(() => useContactForm())
      
      expect(result.current.formRef).toBeDefined()
      expect(result.current.formRef.current).toBeNull()
    })
  })

  describe('Form Validation', () => {
    it('validates required name field', () => {
      const { result } = renderHook(() => useContactForm())
      
      const error = result.current.validateForm({
        name: '',
        email: '<EMAIL>',
        company: '',
        phone: '',
        message: 'Test message',
        service: '',
      })
      
      expect(error).toBe('El nombre es requerido')
    })

    it('validates required email field', () => {
      const { result } = renderHook(() => useContactForm())
      
      const error = result.current.validateForm({
        name: 'Test User',
        email: '',
        company: '',
        phone: '',
        message: 'Test message',
        service: '',
      })
      
      expect(error).toBe('El email es requerido')
    })

    it('validates email format', () => {
      const { result } = renderHook(() => useContactForm())
      
      const error = result.current.validateForm({
        name: 'Test User',
        email: 'invalid-email',
        company: '',
        phone: '',
        message: 'Test message',
        service: '',
      })
      
      expect(error).toBe('El email no tiene un formato válido')
    })

    it('validates required message field', () => {
      const { result } = renderHook(() => useContactForm())
      
      const error = result.current.validateForm({
        name: 'Test User',
        email: '<EMAIL>',
        company: '',
        phone: '',
        message: '',
        service: '',
      })
      
      expect(error).toBe('El mensaje es requerido')
    })

    it('validates message minimum length', () => {
      const { result } = renderHook(() => useContactForm())
      
      const error = result.current.validateForm({
        name: 'Test User',
        email: '<EMAIL>',
        company: '',
        phone: '',
        message: 'Short',
        service: '',
      })
      
      expect(error).toBe('El mensaje debe tener al menos 10 caracteres')
    })

    it('returns null for valid form data', () => {
      const { result } = renderHook(() => useContactForm())
      
      const error = result.current.validateForm({
        name: 'Test User',
        email: '<EMAIL>',
        company: 'Test Company',
        phone: '+34 ***********',
        message: 'This is a valid test message',
        service: 'formacion',
      })
      
      expect(error).toBeNull()
    })
  })

  describe('Form Field Changes', () => {
    it('handles name field changes', () => {
      const { result } = renderHook(() => useContactForm())
      
      act(() => {
        result.current.handleChange({
          target: { name: 'user_name', value: 'John Doe' }
        } as React.ChangeEvent<HTMLInputElement>)
      })
      
      expect(result.current.formData.name).toBe('John Doe')
    })

    it('handles email field changes', () => {
      const { result } = renderHook(() => useContactForm())
      
      act(() => {
        result.current.handleChange({
          target: { name: 'user_email', value: '<EMAIL>' }
        } as React.ChangeEvent<HTMLInputElement>)
      })
      
      expect(result.current.formData.email).toBe('<EMAIL>')
    })

    it('handles other field changes', () => {
      const { result } = renderHook(() => useContactForm())
      
      act(() => {
        result.current.handleChange({
          target: { name: 'message', value: 'Test message content' }
        } as React.ChangeEvent<HTMLTextAreaElement>)
      })
      
      expect(result.current.formData.message).toBe('Test message content')
    })

    it('clears error status when user starts typing', () => {
      const { result } = renderHook(() => useContactForm())
      
      // Set an error status
      act(() => {
        result.current.handleSubmit({
          preventDefault: jest.fn()
        } as any)
      })
      
      // Change a field
      act(() => {
        result.current.handleChange({
          target: { name: 'user_name', value: 'John' }
        } as React.ChangeEvent<HTMLInputElement>)
      })
      
      expect(result.current.formStatus.type).toBeNull()
    })
  })

  describe('Form Submission', () => {
    it('prevents submission with invalid data', async () => {
      const { result } = renderHook(() => useContactForm())
      const preventDefault = jest.fn()
      
      await act(async () => {
        await result.current.handleSubmit({
          preventDefault
        } as any)
      })
      
      expect(preventDefault).toHaveBeenCalled()
      expect(result.current.formStatus.type).toBe('error')
      expect(result.current.isSubmitting).toBe(false)
    })

    it('submits valid form data', async () => {
      const emailjs = require('@emailjs/browser')
      const { result } = renderHook(() => useContactForm())
      
      // Set valid form data
      act(() => {
        result.current.handleChange({
          target: { name: 'user_name', value: 'John Doe' }
        } as React.ChangeEvent<HTMLInputElement>)
      })
      
      act(() => {
        result.current.handleChange({
          target: { name: 'user_email', value: '<EMAIL>' }
        } as React.ChangeEvent<HTMLInputElement>)
      })
      
      act(() => {
        result.current.handleChange({
          target: { name: 'message', value: 'This is a test message' }
        } as React.ChangeEvent<HTMLTextAreaElement>)
      })
      
      // Mock form ref
      result.current.formRef.current = document.createElement('form')
      
      await act(async () => {
        await result.current.handleSubmit({
          preventDefault: jest.fn()
        } as any)
      })
      
      expect(emailjs.sendForm).toHaveBeenCalled()
      expect(result.current.formStatus.type).toBe('success')
    })

    it('handles submission errors', async () => {
      const emailjs = require('@emailjs/browser')
      emailjs.sendForm.mockRejectedValueOnce(new Error('Network error'))
      
      const { result } = renderHook(() => useContactForm())
      
      // Set valid form data
      act(() => {
        result.current.handleChange({
          target: { name: 'user_name', value: 'John Doe' }
        } as React.ChangeEvent<HTMLInputElement>)
      })
      
      act(() => {
        result.current.handleChange({
          target: { name: 'user_email', value: '<EMAIL>' }
        } as React.ChangeEvent<HTMLInputElement>)
      })
      
      act(() => {
        result.current.handleChange({
          target: { name: 'message', value: 'This is a test message' }
        } as React.ChangeEvent<HTMLTextAreaElement>)
      })
      
      // Mock form ref
      result.current.formRef.current = document.createElement('form')
      
      await act(async () => {
        await result.current.handleSubmit({
          preventDefault: jest.fn()
        } as any)
      })
      
      expect(result.current.formStatus.type).toBe('error')
      expect(result.current.isSubmitting).toBe(false)
    })
  })

  describe('Form Reset', () => {
    it('resets form data to initial state', () => {
      const { result } = renderHook(() => useContactForm())
      
      // Set some form data
      act(() => {
        result.current.handleChange({
          target: { name: 'user_name', value: 'John Doe' }
        } as React.ChangeEvent<HTMLInputElement>)
      })
      
      // Reset form
      act(() => {
        result.current.resetForm()
      })
      
      expect(result.current.formData.name).toBe('')
      expect(result.current.formStatus.type).toBeNull()
    })
  })

  describe('Computed Values', () => {
    it('calculates isValid correctly', () => {
      const { result } = renderHook(() => useContactForm())
      
      expect(result.current.isValid).toBe(false)
      
      // Set valid data
      act(() => {
        result.current.handleChange({
          target: { name: 'user_name', value: 'John Doe' }
        } as React.ChangeEvent<HTMLInputElement>)
      })
      
      act(() => {
        result.current.handleChange({
          target: { name: 'user_email', value: '<EMAIL>' }
        } as React.ChangeEvent<HTMLInputElement>)
      })
      
      act(() => {
        result.current.handleChange({
          target: { name: 'message', value: 'This is a valid test message' }
        } as React.ChangeEvent<HTMLTextAreaElement>)
      })
      
      expect(result.current.isValid).toBe(true)
    })

    it('calculates hasChanges correctly', () => {
      const { result } = renderHook(() => useContactForm())
      
      expect(result.current.hasChanges).toBe(false)
      
      act(() => {
        result.current.handleChange({
          target: { name: 'user_name', value: 'John' }
        } as React.ChangeEvent<HTMLInputElement>)
      })
      
      expect(result.current.hasChanges).toBe(true)
    })
  })
})
