'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Target, Eye, Lightbulb, Users } from 'lucide-react'
import { useThemeStyles } from '@/shared/ui-system'

interface MissionVisionProps {
  className?: string
}

/**
 * Mission and Vision Section Component
 * Extracted from about/page.tsx for better modularity
 */
const MissionVision: React.FC<MissionVisionProps> = ({ className = '' }) => {
  const themeStyles = useThemeStyles()
  
  // Mission and Vision data
  const missionVisionData = [
    {
      icon: Target,
      title: 'Nuestra Misión',
      description: 'Democratizar el acceso a la inteligencia artificial, proporcionando soluciones innovadoras y personalizadas que impulsen el crecimiento y la eficiencia de las empresas.',
      gradient: 'from-blue-500 to-cyan-500',
    },
    {
      icon: Eye,
      title: 'Nuestra Visión',
      description: 'Ser la empresa líder en soluciones de IA accesibles, transformando la manera en que las organizaciones operan y toman decisiones en la era digital.',
      gradient: 'from-purple-500 to-pink-500',
    },
    {
      icon: Lightbulb,
      title: 'Innovación',
      description: 'Mantenernos a la vanguardia de las tecnologías emergentes, desarrollando soluciones que anticipen las necesidades futuras de nuestros clientes.',
      gradient: 'from-yellow-500 to-orange-500',
    },
    {
      icon: Users,
      title: 'Compromiso',
      description: 'Construir relaciones duraderas con nuestros clientes, basadas en la confianza, la transparencia y el éxito mutuo.',
      gradient: 'from-green-500 to-teal-500',
    },
  ]
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  }
  
  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' },
    },
  }
  
  const cardVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.5, ease: 'easeOut' },
    },
  }
  
  return (
    <section className={`py-20 ${themeStyles.background.primary} relative overflow-hidden ${className}`}>
      {/* Background effects */}
      <div className="absolute top-1/4 left-0 w-1/3 h-1/2 rounded-full filter blur-[120px] bg-blue-500/5" />
      <div className="absolute bottom-1/4 right-0 w-1/3 h-1/2 rounded-full filter blur-[120px] bg-purple-500/5" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 relative z-10">
        <motion.div
          className="text-center mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: '-100px' }}
          variants={containerVariants}
        >
          <motion.h2 
            className="text-4xl font-bold mb-4 text-glow"
            variants={itemVariants}
          >
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-blue-300 to-blue-200">
              Misión y Visión
            </span>
          </motion.h2>
          <motion.p 
            className={`${themeStyles.text.secondary} max-w-3xl mx-auto`}
            variants={itemVariants}
          >
            Nuestros principios fundamentales que guían cada decisión y acción
          </motion.p>
        </motion.div>
        
        {/* Mission and Vision Cards */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 gap-8"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: '-100px' }}
          variants={containerVariants}
        >
          {missionVisionData.map((item, index) => {
            const Icon = item.icon
            
            return (
              <motion.div
                key={index}
                className={`
                  ${themeStyles.background.elevated} 
                  rounded-2xl p-8 shadow-lg
                  transition-all duration-300
                  hover:shadow-2xl
                `}
                variants={cardVariants}
                whileHover={{ 
                  y: -5,
                  transition: { duration: 0.2 }
                }}
              >
                {/* Icon */}
                <div className={`
                  w-16 h-16 rounded-xl mb-6 flex items-center justify-center
                  bg-gradient-to-r ${item.gradient}
                `}>
                  <Icon className="w-8 h-8 text-white" />
                </div>
                
                {/* Content */}
                <h3 className={`text-2xl font-bold mb-4 ${themeStyles.text.primary}`}>
                  {item.title}
                </h3>
                <p className={`${themeStyles.text.secondary} leading-relaxed`}>
                  {item.description}
                </p>
                
                {/* Decorative element */}
                <div className={`
                  mt-6 w-12 h-1 rounded-full
                  bg-gradient-to-r ${item.gradient}
                `} />
              </motion.div>
            )
          })}
        </motion.div>
      </div>
    </section>
  )
}

export default MissionVision
