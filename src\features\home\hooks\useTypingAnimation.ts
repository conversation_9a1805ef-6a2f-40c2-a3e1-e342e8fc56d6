'use client'

import { useState, useEffect, useRef, useCallback } from 'react'

// Types
interface TypingConfig {
  phrases: string[]
  typingSpeed?: number
  deletingSpeed?: number
  pauseDuration?: number
  loop?: boolean
}

interface TypingState {
  currentText: string
  currentPhraseIndex: number
  isDeleting: boolean
  isTyping: boolean
  isPaused: boolean
}

// Default configuration
const DEFAULT_CONFIG: Required<Omit<TypingConfig, 'phrases'>> = {
  typingSpeed: 150,
  deletingSpeed: 80,
  pauseDuration: 1500,
  loop: true,
}

/**
 * Custom hook for typing animation effect
 * Extracted from HeroSection for reusability and testability
 * 
 * @param config - Configuration for the typing animation
 * @returns Object containing current text and animation state
 * 
 * @example
 * ```tsx
 * const typing = useTypingAnimation({
 *   phrases: ['Hello World', 'Welcome', 'InformatiK-AI'],
 *   typingSpeed: 100,
 *   deletingSpeed: 50
 * })
 * 
 * return <span>{typing.currentText}</span>
 * ```
 */
export const useTypingAnimation = (config: TypingConfig) => {
  const { phrases, typingSpeed, deletingSpeed, pauseDuration, loop } = {
    ...DEFAULT_CONFIG,
    ...config,
  }

  // State
  const [state, setState] = useState<TypingState>({
    currentText: '',
    currentPhraseIndex: 0,
    isDeleting: false,
    isTyping: false,
    isPaused: false,
  })

  // Refs
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const isMountedRef = useRef(true)

  // Clear timeout helper
  const clearCurrentTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
  }, [])

  // Main typing logic
  const processTyping = useCallback(() => {
    if (!isMountedRef.current || phrases.length === 0) return

    const currentPhrase = phrases[state.currentPhraseIndex]

    setState(prevState => {
      const { currentText, isDeleting, currentPhraseIndex } = prevState

      // Determine next state
      if (!isDeleting && currentText === currentPhrase) {
        // Finished typing current phrase, pause then start deleting
        timeoutRef.current = setTimeout(() => {
          setState(prev => ({ ...prev, isDeleting: true, isPaused: false }))
        }, pauseDuration)
        
        return { ...prevState, isPaused: true, isTyping: false }
      }

      if (isDeleting && currentText === '') {
        // Finished deleting, move to next phrase
        const nextIndex = loop 
          ? (currentPhraseIndex + 1) % phrases.length
          : Math.min(currentPhraseIndex + 1, phrases.length - 1)

        timeoutRef.current = setTimeout(() => {
          setState(prev => ({
            ...prev,
            currentPhraseIndex: nextIndex,
            isDeleting: false,
            isPaused: false,
            isTyping: true,
          }))
        }, 200) // Short pause between phrases

        return { ...prevState, isPaused: true, isTyping: false }
      }

      // Continue typing or deleting
      const speed = isDeleting ? deletingSpeed : typingSpeed
      const nextText = isDeleting
        ? currentText.substring(0, currentText.length - 1)
        : currentPhrase.substring(0, currentText.length + 1)

      timeoutRef.current = setTimeout(processTyping, speed)

      return {
        ...prevState,
        currentText: nextText,
        isTyping: true,
        isPaused: false,
      }
    })
  }, [phrases, state.currentPhraseIndex, typingSpeed, deletingSpeed, pauseDuration, loop])

  // Start animation effect
  useEffect(() => {
    if (phrases.length === 0) return

    // Start typing animation
    timeoutRef.current = setTimeout(processTyping, typingSpeed)

    return () => {
      clearCurrentTimeout()
    }
  }, [phrases, processTyping, typingSpeed, clearCurrentTimeout])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false
      clearCurrentTimeout()
    }
  }, [clearCurrentTimeout])

  // Reset animation when phrases change
  useEffect(() => {
    setState({
      currentText: '',
      currentPhraseIndex: 0,
      isDeleting: false,
      isTyping: false,
      isPaused: false,
    })
  }, [phrases])

  // Public API
  return {
    currentText: state.currentText,
    isTyping: state.isTyping,
    isPaused: state.isPaused,
    currentPhrase: phrases[state.currentPhraseIndex] || '',
    progress: state.currentText.length / (phrases[state.currentPhraseIndex]?.length || 1),
    
    // Control methods
    pause: useCallback(() => {
      clearCurrentTimeout()
      setState(prev => ({ ...prev, isPaused: true, isTyping: false }))
    }, [clearCurrentTimeout]),
    
    resume: useCallback(() => {
      if (state.isPaused) {
        setState(prev => ({ ...prev, isPaused: false }))
        timeoutRef.current = setTimeout(processTyping, 100)
      }
    }, [state.isPaused, processTyping]),
    
    reset: useCallback(() => {
      clearCurrentTimeout()
      setState({
        currentText: '',
        currentPhraseIndex: 0,
        isDeleting: false,
        isTyping: false,
        isPaused: false,
      })
    }, [clearCurrentTimeout]),
  }
}

export default useTypingAnimation
