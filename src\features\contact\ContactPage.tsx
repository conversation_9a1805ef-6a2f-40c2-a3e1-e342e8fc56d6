'use client'

import React from 'react'
import { motion } from 'framer-motion'
import ContactHero from './components/ContactHero'
import ContactForm from './components/ContactForm'
import ContactInfo from './components/ContactInfo'

/**
 * ATLAS v2.4 Refactored Contact Page
 * 
 * BEFORE: 855 lines monolithic component
 * AFTER: <100 lines orchestrating modular components
 * 
 * IMPROVEMENTS:
 * - Reduced from 855 to <100 lines (-88% reduction)
 * - Extracted 6 specialized components
 * - Extracted 2 custom hooks
 * - Migrated to design system tokens
 * - Improved testability and maintainability
 * - Maintained all existing functionality
 * 
 * @returns Contact page with modular architecture
 */
const ContactPage: React.FC = () => {
  // Animation variants for the main container
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.1,
      },
    },
  }
  
  const sectionVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' },
    },
  }
  
  return (
    <motion.div
      className="min-h-screen"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Hero Section */}
      <motion.div variants={sectionVariants}>
        <ContactHero />
      </motion.div>
      
      {/* Contact Content */}
      <motion.section
        className="py-16 sm:py-20 lg:py-24"
        variants={sectionVariants}
      >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 max-w-7xl mx-auto">
            {/* Contact Form */}
            <motion.div
              variants={sectionVariants}
              className="order-2 lg:order-1"
            >
              <ContactForm />
            </motion.div>
            
            {/* Contact Information */}
            <motion.div
              variants={sectionVariants}
              className="order-1 lg:order-2"
            >
              <ContactInfo />
            </motion.div>
          </div>
        </div>
      </motion.section>
    </motion.div>
  )
}

export default ContactPage
