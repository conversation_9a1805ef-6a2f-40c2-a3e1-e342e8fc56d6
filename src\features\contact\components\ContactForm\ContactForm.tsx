'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Button, useThemeStyles } from '@/shared/ui-system'
import { useContactForm } from '../../hooks/useContactForm'
import FormField from '../FormField/FormField'
import FormStatus from '../FormStatus/FormStatus'

interface ContactFormProps {
  className?: string
}

/**
 * Contact Form Component
 * Main form component with validation and EmailJS integration
 * Extracted from contact/page.tsx following ATLAS v2.4 principles
 */
const ContactForm: React.FC<ContactFormProps> = ({ className = '' }) => {
  const themeStyles = useThemeStyles()
  const {
    formData,
    formStatus,
    isSubmitting,
    formRef,
    handleChange,
    handleSubmit,
    isValid,
  } = useContactForm()
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  }
  
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4 },
    },
  }
  
  return (
    <motion.div
      className={`${themeStyles.background.elevated} rounded-2xl p-8 shadow-2xl ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants}>
        <h2 className={`text-3xl font-bold mb-8 ${themeStyles.text.primary} text-center`}>
          Envíanos un mensaje
        </h2>
      </motion.div>
      
      <form ref={formRef} onSubmit={handleSubmit} className="space-y-6">
        {/* Name Field */}
        <motion.div variants={itemVariants}>
          <FormField
            label="Nombre completo"
            name="user_name"
            type="text"
            value={formData.name}
            onChange={handleChange}
            required
            placeholder="Tu nombre completo"
          />
        </motion.div>
        
        {/* Email Field */}
        <motion.div variants={itemVariants}>
          <FormField
            label="Email"
            name="user_email"
            type="email"
            value={formData.email}
            onChange={handleChange}
            required
            placeholder="<EMAIL>"
          />
        </motion.div>
        
        {/* Company Field */}
        <motion.div variants={itemVariants}>
          <FormField
            label="Empresa (opcional)"
            name="company"
            type="text"
            value={formData.company}
            onChange={handleChange}
            placeholder="Nombre de tu empresa"
          />
        </motion.div>
        
        {/* Phone Field */}
        <motion.div variants={itemVariants}>
          <FormField
            label="Teléfono (opcional)"
            name="phone"
            type="tel"
            value={formData.phone}
            onChange={handleChange}
            placeholder="+34 ***********"
          />
        </motion.div>
        
        {/* Service Field */}
        <motion.div variants={itemVariants}>
          <FormField
            label="Servicio de interés"
            name="service"
            type="select"
            value={formData.service}
            onChange={handleChange}
            options={[
              { value: '', label: 'Selecciona un servicio' },
              { value: 'formacion', label: 'Formación In Company' },
              { value: 'asesoria', label: 'Asesoría Estratégica' },
              { value: 'desarrollo', label: 'Desarrollo de Cursos' },
              { value: 'automatizacion', label: 'Automatizaciones' },
              { value: 'desarrollo-medida', label: 'Desarrollo a Medida' },
              { value: 'consultoria', label: 'Consultoría General' },
            ]}
          />
        </motion.div>
        
        {/* Message Field */}
        <motion.div variants={itemVariants}>
          <FormField
            label="Mensaje"
            name="message"
            type="textarea"
            value={formData.message}
            onChange={handleChange}
            required
            placeholder="Cuéntanos sobre tu proyecto o consulta..."
            rows={5}
          />
        </motion.div>
        
        {/* Form Status */}
        <motion.div variants={itemVariants}>
          <FormStatus status={formStatus} />
        </motion.div>
        
        {/* Submit Button */}
        <motion.div variants={itemVariants}>
          <Button
            type="submit"
            variant="gradient"
            size="lg"
            loading={isSubmitting}
            disabled={!isValid || isSubmitting}
            fullWidth
            effect="glow"
            className="text-lg font-semibold"
          >
            {isSubmitting ? 'Enviando mensaje...' : 'Enviar mensaje'}
          </Button>
        </motion.div>
      </form>
    </motion.div>
  )
}

export default ContactForm
