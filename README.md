# InformatiK-AI Website

Sitio web oficial de InformatiK-AI, especialistas en soluciones de Inteligencia Artificial.

**🚀 Desarrollado con ATLAS v2.4** - Metodología de desarrollo holístico para ventures digitales.

## 🏗️ Arquitectura

Este proyecto sigue los principios de **ATLAS v2.4**, implementando:

- **Arquitectura modular** con componentes especializados
- **Testing automatizado** con Jest + React Testing Library
- **Documentación viva** con Storybook
- **Quality gates** automatizados con CI/CD
- **Performance monitoring** con métricas ATLAS

## ✅ Estado ATLAS v2.4

### SPRINT 1.1 - CONFIGURACIÓN DE HERRAMIENTAS ✅ (COMPLETADO)
- ✅ Testing framework (Jest + React Testing Library)
- ✅ Storybook para desarrollo de componentes
- ✅ CI/CD pipeline con GitHub Actions
- ✅ Quality gates automatizados
- ✅ Pre-commit hooks con Husky

### SPRINT 1.2 - LIMPIEZA Y OPTIMIZACIÓN ⏳ (EN PROGRESO)
- ✅ Limpieza de código muerto y comentarios
- ✅ Optimización de scripts y configuraciones
- ✅ Bundle analyzer con límites ATLAS
- ✅ Métricas de performance baseline
- ⏳ Documentación actualizada

### CASO PILOTO - HeroSection Refactorizado ✅ (COMPLETADO)
- ✅ Reducción de 269 → <100 líneas (-63%)
- ✅ Extracción de hook useTypingAnimation
- ✅ Arquitectura modular (5 componentes especializados)
- ✅ Tests unitarios con >85% cobertura
- ✅ Storybook stories implementadas

## 📊 Métricas ATLAS Actuales

### Calidad de Código
- **Componentes >200 líneas**: 4 → 3 (-25%) 🎯
- **Test coverage**: 0% → 15% (archivos nuevos) 📈
- **TypeScript strict**: ✅ Habilitado
- **ESLint compliance**: 100% (archivos nuevos) ✅

### Performance
- **Bundle size target**: <1.5MB 🎯
- **Build time**: Optimizado con scripts paralelos ⚡
- **Quality gates**: 5 jobs automatizados 🤖

## 🚀 Tecnologías

### Core Stack
- **Next.js 15.3.1** - Framework de React para producción
- **TypeScript** - Tipado estático con modo strict
- **Tailwind CSS** - Framework de CSS utilitario
- **Framer Motion** - Biblioteca de animaciones para React

### Development & Quality
- **Jest + React Testing Library** - Testing framework completo
- **Storybook 8.4.7** - Desarrollo de componentes aislados
- **ESLint + Prettier** - Linting y formateo automatizado
- **Husky + lint-staged** - Pre-commit hooks
- **GitHub Actions** - CI/CD pipeline automatizado

### UI & UX
- **React Hook Form** - Manejo eficiente de formularios
- **Lucide React** - Iconos modernos y ligeros
- **Sistema de temas** - Dark/Light mode con persistencia
- **Hosting**: Hostinger (Static Export)

## 📁 Estructura del Proyecto ATLAS v2.4

```
src/
├── app/                    # App Router de Next.js
├── components/             # Componentes organizados por funcionalidad
│   ├── home/              # Componentes de la página principal
│   ├── layout/            # Componentes de layout (Header, Footer)
│   └── ui/                # Componentes reutilizables + Storybook
├── features/               # Funcionalidades encapsuladas (ATLAS)
│   └── home/
│       ├── components/    # Componentes modulares del HeroSection
│       └── hooks/         # Hooks especializados (useTypingAnimation)
├── context/               # Context providers optimizados
├── hooks/                 # Custom hooks globales
├── utils/                 # Utilidades puras
└── __tests__/             # Tests globales
```

## 📦 Instalación

```bash
# Clonar el repositorio
git clone [URL_DEL_REPOSITORIO]

# Instalar dependencias
npm install

# Configurar hooks de git
npm run prepare

# Ejecutar en modo desarrollo
npm run dev
```

## 🛠️ Scripts Disponibles

### Desarrollo
```bash
npm run dev              # Servidor de desarrollo (puerto 3006)
npm run build            # Build de producción
npm run start            # Servidor de producción (puerto 3001)
npm run clean            # Limpia archivos de build
```

### Calidad de Código
```bash
npm run lint             # Ejecuta ESLint
npm run lint:fix         # Corrige errores de ESLint automáticamente
npm run format           # Formatea código con Prettier
npm run format:check     # Verifica formato sin cambios
npm run type-check       # Verifica tipos de TypeScript
npm run quality          # Ejecuta todas las verificaciones
npm run quality:fix      # Corrige todos los problemas automáticamente
```

### Testing
```bash
npm run test             # Ejecuta tests en modo watch
npm run test:coverage    # Ejecuta tests con reporte de cobertura
npm run test:ci          # Ejecuta tests para CI/CD
```

### Documentación
```bash
npm run storybook        # Inicia Storybook (puerto 6006)
npm run build-storybook  # Build de Storybook para producción
```

### Análisis y Métricas
```bash
npm run analyze          # Analiza tamaño del bundle con límites ATLAS
npm run analyze:webpack  # Análisis detallado con webpack-bundle-analyzer
npm run baseline         # Genera métricas baseline de performance
npm run metrics          # Ejecuta análisis completo de métricas
```

## 🔧 Configuración ATLAS v2.4

### Quality Gates Automatizados
- **Pre-commit hooks**: Linting, formateo y type checking automático
- **CI/CD Pipeline**: 5 jobs automatizados en GitHub Actions
- **Bundle size limits**: Máximo 1.5MB según estándares ATLAS
- **Test coverage**: Mínimo 80% para componentes nuevos

### TypeScript
- ✅ **Modo estricto habilitado** con verificación completa de tipos
- ✅ **Reglas avanzadas** configuradas
- ✅ **Import organization** automatizada

### ESLint & Prettier
- ✅ **Configuración estricta** con reglas para React/TypeScript
- ✅ **Reglas de accesibilidad** (jsx-a11y)
- ✅ **Formateo automático** en pre-commit

## 📚 Guías de Desarrollo

### Testing
- Usa `npm run test:watch` durante desarrollo
- Mantén cobertura >80% en componentes nuevos
- Escribe tests para hooks personalizados
- Usa Storybook para desarrollo visual

### Componentes
- Máximo 200 líneas por componente
- Un componente = una responsabilidad
- Usa TypeScript strict mode
- Documenta props con JSDoc

### Commits
- Los pre-commit hooks se ejecutan automáticamente
- Formato de código automático
- Type checking obligatorio
- Tests relacionados ejecutados

## 🚀 Próximos Sprints ATLAS

### Sprint 2.1 - Sistema de Diseño Unificado
- [ ] Consolidar sistema de botones
- [ ] Crear design tokens centralizados
- [ ] Implementar componentes atómicos

### Sprint 2.2 - Refactorización Masiva
- [ ] Refactorizar contact/page.tsx (855 líneas)
- [ ] Refactorizar about/page.tsx (601 líneas)
- [ ] Optimizar componentes >200 líneas

## 🤝 Contribución

Este proyecto sigue la metodología **ATLAS v2.4** para desarrollo holístico:

1. **Fork** el repositorio
2. **Crea** una rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. **Ejecuta** tests y quality checks (`npm run quality`)
4. **Commit** tus cambios (pre-commit hooks se ejecutan automáticamente)
5. **Push** a la rama (`git push origin feature/nueva-funcionalidad`)
6. **Abre** un Pull Request

### Funcionalidad Crítica a Preservar
- **Chatbot en `/chatbot`**: Implementación funcional con iframe de Botsonic
- **Configuración CSP**: Headers de seguridad en `public/security-headers.js`
- **Static export**: Configuración para Hostinger

---

**🚀 Desarrollado con ATLAS v2.4** - Transformando ideas en realidad con IA
