#!/usr/bin/env node

/**
 * ATLAS v2.4 Bundle Analyzer Script
 * Analyzes bundle size and provides recommendations
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// ATLAS v2.4 Bundle Size Limits (in bytes)
const ATLAS_LIMITS = {
  TOTAL_BUNDLE: 1.5 * 1024 * 1024, // 1.5MB
  MAIN_CHUNK: 500 * 1024, // 500KB
  VENDOR_CHUNK: 800 * 1024, // 800KB
  CSS_BUNDLE: 100 * 1024, // 100KB
};

// Colors for console output
const colors = {
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzeBundleSize() {
  log('\n🚀 ATLAS v2.4 Bundle Analysis', 'blue');
  log('================================', 'blue');

  const outDir = path.join(process.cwd(), 'out');
  
  if (!fs.existsSync(outDir)) {
    log('❌ Build output not found. Run "npm run build" first.', 'red');
    process.exit(1);
  }

  // Calculate total bundle size
  let totalSize = 0;
  let jsSize = 0;
  let cssSize = 0;
  let htmlSize = 0;
  let imageSize = 0;

  function calculateDirSize(dir, category = 'total') {
    const files = fs.readdirSync(dir);
    let size = 0;

    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        size += calculateDirSize(filePath, category);
      } else {
        const fileSize = stat.size;
        size += fileSize;

        // Categorize files
        const ext = path.extname(file).toLowerCase();
        if (ext === '.js') jsSize += fileSize;
        else if (ext === '.css') cssSize += fileSize;
        else if (ext === '.html') htmlSize += fileSize;
        else if (['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp'].includes(ext)) {
          imageSize += fileSize;
        }
      }
    });

    return size;
  }

  totalSize = calculateDirSize(outDir);

  // Display results
  log('\n📊 Bundle Size Analysis:', 'bold');
  log(`Total Bundle Size: ${formatBytes(totalSize)}`, totalSize > ATLAS_LIMITS.TOTAL_BUNDLE ? 'red' : 'green');
  log(`JavaScript: ${formatBytes(jsSize)}`, jsSize > ATLAS_LIMITS.MAIN_CHUNK ? 'yellow' : 'green');
  log(`CSS: ${formatBytes(cssSize)}`, cssSize > ATLAS_LIMITS.CSS_BUNDLE ? 'yellow' : 'green');
  log(`HTML: ${formatBytes(htmlSize)}`, 'blue');
  log(`Images: ${formatBytes(imageSize)}`, 'blue');

  // Check against ATLAS limits
  log('\n🎯 ATLAS v2.4 Quality Gates:', 'bold');
  
  const checks = [
    {
      name: 'Total Bundle Size',
      current: totalSize,
      limit: ATLAS_LIMITS.TOTAL_BUNDLE,
      critical: true
    },
    {
      name: 'JavaScript Bundle',
      current: jsSize,
      limit: ATLAS_LIMITS.MAIN_CHUNK,
      critical: false
    },
    {
      name: 'CSS Bundle',
      current: cssSize,
      limit: ATLAS_LIMITS.CSS_BUNDLE,
      critical: false
    }
  ];

  let hasFailures = false;

  checks.forEach(check => {
    const passed = check.current <= check.limit;
    const percentage = ((check.current / check.limit) * 100).toFixed(1);
    
    if (passed) {
      log(`✅ ${check.name}: ${formatBytes(check.current)} (${percentage}% of limit)`, 'green');
    } else {
      log(`❌ ${check.name}: ${formatBytes(check.current)} (${percentage}% of limit - EXCEEDS!)`, 'red');
      if (check.critical) hasFailures = true;
    }
  });

  // Recommendations
  if (hasFailures || totalSize > ATLAS_LIMITS.TOTAL_BUNDLE * 0.8) {
    log('\n💡 Optimization Recommendations:', 'yellow');
    
    if (jsSize > ATLAS_LIMITS.MAIN_CHUNK * 0.8) {
      log('• Consider code splitting and lazy loading for large components', 'yellow');
      log('• Review and optimize third-party dependencies', 'yellow');
      log('• Use dynamic imports for non-critical features', 'yellow');
    }
    
    if (cssSize > ATLAS_LIMITS.CSS_BUNDLE * 0.8) {
      log('• Remove unused CSS classes', 'yellow');
      log('• Consider CSS-in-JS for component-specific styles', 'yellow');
    }
    
    if (imageSize > 500 * 1024) {
      log('• Optimize images using next/image or external CDN', 'yellow');
      log('• Convert images to WebP format', 'yellow');
      log('• Use appropriate image sizes for different viewports', 'yellow');
    }
  } else {
    log('\n🎉 Bundle size is within ATLAS v2.4 limits!', 'green');
  }

  // Exit with error if critical limits exceeded
  if (hasFailures) {
    log('\n❌ Bundle analysis failed. Critical size limits exceeded.', 'red');
    process.exit(1);
  } else {
    log('\n✅ Bundle analysis passed!', 'green');
  }
}

// Run analysis
try {
  analyzeBundleSize();
} catch (error) {
  log(`❌ Error during bundle analysis: ${error.message}`, 'red');
  process.exit(1);
}
