'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { useThemeStyles } from '@/shared/ui-system'
import NeuralBackground from '@/features/contact/components/NeuralBackground'

interface AboutHeroProps {
  className?: string
}

/**
 * About Hero Section Component
 * Extracted from about/page.tsx following ATLAS v2.4 principles
 */
const AboutHero: React.FC<AboutHeroProps> = ({ className = '' }) => {
  const themeStyles = useThemeStyles()
  
  // Animation variants
  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2,
      },
    },
  }
  
  const fadeInUp = {
    hidden: { opacity: 0, y: 40 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.9,
        ease: [0.22, 1, 0.36, 1],
      },
    },
  }
  
  return (
    <section className={`relative py-32 md:py-44 overflow-hidden code-lines-bg bg-gradient-to-br from-gray-950 via-gray-900 to-gray-800 ${className}`}>
      {/* Glow effect */}
      <div className="absolute top-0 left-1/4 w-1/2 h-1/3 rounded-full filter blur-[120px] bg-blue-900/30" />
      
      {/* Neural Network Background */}
      <NeuralBackground />
      
      {/* Grid pattern and scan effects */}
      <div className="absolute inset-0 bg-grid-white/[0.03] bg-[length:40px_40px]" />
      <div className="scan-effect absolute inset-0 opacity-30" />
      <div className="matrix-bg absolute inset-0 opacity-10" />
      
      {/* Content */}
      <motion.div
        className="container relative z-10 mx-auto px-4 sm:px-6 lg:px-8"
        initial="hidden"
        animate="visible"
        variants={staggerContainer}
      >
        <div className="max-w-5xl mx-auto text-center">
          <motion.h1
            className="text-5xl md:text-6xl lg:text-7xl font-extrabold leading-tight text-white mb-6 text-glow"
            variants={fadeInUp}
          >
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-blue-300 to-blue-200">
              Sobre Informatik-AI
            </span>
          </motion.h1>
          
          <motion.p
            className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto"
            variants={fadeInUp}
          >
            Nuestra misión es transformar empresas a través del poder de la
            inteligencia artificial.
          </motion.p>
        </div>
      </motion.div>
    </section>
  )
}

export default AboutHero
