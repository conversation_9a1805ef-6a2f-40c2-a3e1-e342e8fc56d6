/**
 * ATLAS v2.4 - Optimized Render Hook
 * Provides utilities for performance optimization
 */

import { useCallback, useMemo, useRef, useEffect } from 'react'

/**
 * Hook for memoizing expensive calculations
 */
export const useExpensiveMemo = <T>(
  factory: () => T,
  deps: React.DependencyList
): T => {
  return useMemo(factory, deps)
}

/**
 * Hook for stable callback references
 */
export const useStableCallback = <T extends (...args: any[]) => any>(
  callback: T
): T => {
  const callbackRef = useRef(callback)
  
  useEffect(() => {
    callbackRef.current = callback
  })
  
  return useCallback((...args: any[]) => {
    return callbackRef.current(...args)
  }, []) as T
}

/**
 * Hook for debounced values
 */
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState(value)
  
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)
    
    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])
  
  return debouncedValue
}

/**
 * Hook for throttled callbacks
 */
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const lastRun = useRef(Date.now())
  
  return useCallback((...args: any[]) => {
    if (Date.now() - lastRun.current >= delay) {
      callback(...args)
      lastRun.current = Date.now()
    }
  }, [callback, delay]) as T
}

/**
 * Hook for intersection observer (lazy loading)
 */
export const useIntersectionObserver = (
  options: IntersectionObserverInit = {}
) => {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [hasIntersected, setHasIntersected] = useState(false)
  const elementRef = useRef<HTMLElement>(null)
  
  useEffect(() => {
    const element = elementRef.current
    if (!element) return
    
    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting)
      if (entry.isIntersecting && !hasIntersected) {
        setHasIntersected(true)
      }
    }, options)
    
    observer.observe(element)
    
    return () => {
      observer.unobserve(element)
    }
  }, [options, hasIntersected])
  
  return { elementRef, isIntersecting, hasIntersected }
}

/**
 * Hook for performance monitoring
 */
export const usePerformanceMonitor = (name: string) => {
  const startTime = useRef<number>()
  
  const start = useCallback(() => {
    startTime.current = performance.now()
  }, [])
  
  const end = useCallback(() => {
    if (startTime.current) {
      const duration = performance.now() - startTime.current
      console.log(`[Performance] ${name}: ${duration.toFixed(2)}ms`)
      return duration
    }
    return 0
  }, [name])
  
  return { start, end }
}

/**
 * Hook for component render tracking
 */
export const useRenderTracker = (componentName: string) => {
  const renderCount = useRef(0)
  const lastRenderTime = useRef<number>()
  
  useEffect(() => {
    renderCount.current += 1
    const now = performance.now()
    
    if (lastRenderTime.current) {
      const timeSinceLastRender = now - lastRenderTime.current
      console.log(
        `[Render] ${componentName} rendered ${renderCount.current} times. ` +
        `Time since last render: ${timeSinceLastRender.toFixed(2)}ms`
      )
    }
    
    lastRenderTime.current = now
  })
  
  return renderCount.current
}

// Missing import
import { useState } from 'react'
