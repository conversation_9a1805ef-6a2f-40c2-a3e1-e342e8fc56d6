'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

// Definir un tipo para las partículas
type Particle = {
  id: number;
  top: number;
  left: number;
  size: number;
  duration: number;
  delay: number;
};

const FloatingParticles = () => {
  const [particles, setParticles] = useState<Particle[]>([]);
  const [isMounted, setIsMounted] = useState(false);

  // Generar partículas solo en el cliente
  useEffect(() => {
    const generateParticles = () => {
      const newParticles: Particle[] = [];
      for (let i = 0; i < 15; i++) {
        newParticles.push({
          id: i,
          top: Math.random() * 100,
          left: Math.random() * 100,
          size: Math.random() * 2 + 1,
          duration: Math.random() * 5 + 5,
          delay: Math.random() * 5,
        });
      }
      setParticles(newParticles);
    };

    generateParticles();
    setIsMounted(true);
  }, []);

  // No renderizar nada durante SSR
  if (!isMounted) {
    return null;
  }

  return (
    <div className='absolute inset-0 overflow-hidden pointer-events-none'>
      {particles.map(particle => (
        <motion.div
          key={particle.id}
          className='absolute bg-white rounded-full opacity-30'
          style={{
            top: `${particle.top}%`,
            left: `${particle.left}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
          }}
          animate={{
            y: [0, -100],
            opacity: [0, 0.5, 0],
          }}
          transition={{
            duration: particle.duration,
            repeat: Infinity,
            delay: particle.delay,
          }}
        />
      ))}
    </div>
  );
};

export default FloatingParticles;
