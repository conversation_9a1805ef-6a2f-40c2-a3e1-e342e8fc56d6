{"name": "informatik-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3006", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start -p 3001", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "export": "next build", "serve-static": "npx serve out -p 3002", "serve": "http-server -p 3002", "analyze": "npm run build:analyze", "bundle-size": "npm run build && npx bundlesize"}, "dependencies": {"@emailjs/browser": "^4.4.1", "framer-motion": "^12.7.4", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-intersection-observer": "^9.16.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.3.3", "@types/node": "22.15.17", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "autoprefixer": "^10.4.14", "eslint": "^9", "eslint-config-next": "15.3.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8.4.24", "prettier": "^3.5.3", "serve": "^14.2.4", "tailwindcss": "^3.3.2", "typescript": "^5"}}