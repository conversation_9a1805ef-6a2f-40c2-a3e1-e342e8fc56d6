{"name": "informatik-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3006", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start -p 3001", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "serve-static": "npx serve out -p 3002", "analyze": "npm run build && node scripts/analyze-bundle.js", "analyze:webpack": "npm run build:analyze", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "clean": "rm -rf .next out coverage storybook-static", "clean:install": "npm run clean && rm -rf node_modules package-lock.json && npm install", "quality": "npm run type-check && npm run lint && npm run format:check", "quality:fix": "npm run type-check && npm run lint:fix && npm run format", "pre-commit": "npm run quality:fix && npm run test:ci", "prepare": "husky install", "baseline": "node scripts/performance-baseline.js", "metrics": "npm run baseline && npm run analyze", "validate:design-system": "node scripts/validate-design-system.js", "validate:bundle-size": "node scripts/validate-bundle-size.js", "validate:accessibility": "node scripts/validate-accessibility.js", "validate:performance": "node scripts/validate-performance-budget.js", "migrate:design-system": "node scripts/migrate-to-design-system.js", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "validate:all": "npm run validate:design-system && npm run validate:bundle-size && npm run validate:accessibility && npm run validate:performance && npm run quality && npm run test:ci", "atlas:sprint-3-1": "npm run validate:all && npm run test:e2e", "atlas:quality-gates": "npm run validate:all && npm run test:e2e && echo '✅ All ATLAS v2.4 quality gates passed!'"}, "dependencies": {"@emailjs/browser": "^4.4.1", "clsx": "^2.1.1", "framer-motion": "^12.7.4", "lucide-react": "^0.468.0", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-intersection-observer": "^9.16.0", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.3.3", "@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "^8.4.7", "@storybook/addon-links": "^8.4.7", "@storybook/blocks": "^8.4.7", "@storybook/nextjs": "^8.4.7", "@storybook/react": "^8.4.7", "@storybook/test": "^8.4.7", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@types/node": "22.15.17", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "autoprefixer": "^10.4.14", "eslint": "^9", "eslint-config-next": "15.3.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-storybook": "^0.11.1", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.3.0", "postcss": "^8.4.24", "prettier": "^3.5.3", "serve": "^14.2.4", "storybook": "^8.4.7", "tailwindcss": "^3.3.2", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}