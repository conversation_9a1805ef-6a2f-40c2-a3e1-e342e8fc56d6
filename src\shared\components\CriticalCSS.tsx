'use client'

import { useEffect } from 'react'

/**
 * ATLAS v2.4 - Critical CSS Optimization
 * Optimizes Critical Rendering Path by inlining critical CSS
 * and deferring non-critical stylesheets
 */

const criticalCSS = `
  /* Critical CSS - Above the fold styles */
  
  /* Reset and base styles */
  *,*::before,*::after{box-sizing:border-box}
  html{line-height:1.15;-webkit-text-size-adjust:100%}
  body{margin:0;font-family:var(--font-outfit),system-ui,sans-serif}
  
  /* Layout essentials */
  .min-h-screen{min-height:100vh}
  .flex{display:flex}
  .flex-col{flex-direction:column}
  .flex-grow{flex-grow:1}
  
  /* Typography essentials */
  h1,h2,h3,h4,h5,h6{margin:0;font-weight:600}
  p{margin:0}
  
  /* Color essentials */
  .text-white{color:#fff}
  .text-black{color:#000}
  .bg-white{background-color:#fff}
  .bg-black{background-color:#000}
  
  /* Gradient essentials */
  .bg-gradient-to-r{background-image:linear-gradient(to right,var(--tw-gradient-stops))}
  .from-primary{--tw-gradient-from:#00B4DB;--tw-gradient-to:rgb(0 180 219 / 0);--tw-gradient-stops:var(--tw-gradient-from),var(--tw-gradient-to)}
  .to-secondary{--tw-gradient-to:#48D1CC}
  
  /* Animation essentials */
  .transition-all{transition-property:all;transition-timing-function:cubic-bezier(0.4,0,0.2,1);transition-duration:150ms}
  
  /* Hide elements initially to prevent FOUC */
  .hero-section{opacity:0;animation:fadeIn 0.6s ease-out 0.1s forwards}
  
  @keyframes fadeIn{
    from{opacity:0;transform:translateY(20px)}
    to{opacity:1;transform:translateY(0)}
  }
  
  /* Loading state */
  .loading-skeleton{
    background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0 50%,#f0f0f0 75%);
    background-size:200% 100%;
    animation:loading 1.5s infinite;
  }
  
  @keyframes loading{
    0%{background-position:200% 0}
    100%{background-position:-200% 0}
  }
`

const nonCriticalStylesheets = [
  '/css/animations.css',
  '/css/components.css',
  '/css/utilities.css'
]

/**
 * Critical CSS Component
 * Inlines critical CSS and defers non-critical stylesheets
 */
export const CriticalCSS: React.FC = () => {
  useEffect(() => {
    // Defer non-critical CSS loading
    const loadNonCriticalCSS = () => {
      nonCriticalStylesheets.forEach(href => {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = href
        link.media = 'print'
        link.onload = () => {
          link.media = 'all'
        }
        document.head.appendChild(link)
      })
    }
    
    // Load non-critical CSS after initial render
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', loadNonCriticalCSS)
    } else {
      loadNonCriticalCSS()
    }
    
    return () => {
      document.removeEventListener('DOMContentLoaded', loadNonCriticalCSS)
    }
  }, [])
  
  return (
    <style
      dangerouslySetInnerHTML={{ __html: criticalCSS }}
      data-critical-css="true"
    />
  )
}

/**
 * Resource Hints Component
 * Provides resource hints for better performance
 */
export const ResourceHints: React.FC = () => {
  useEffect(() => {
    // Preload critical resources
    const preloadResources = [
      { href: '/images/hero-bg.webp', as: 'image', type: 'image/webp' },
      { href: '/fonts/outfit-variable.woff2', as: 'font', type: 'font/woff2', crossOrigin: 'anonymous' },
    ]
    
    preloadResources.forEach(resource => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = resource.href
      link.as = resource.as
      if (resource.type) link.type = resource.type
      if (resource.crossOrigin) link.crossOrigin = resource.crossOrigin
      document.head.appendChild(link)
    })
    
    // Prefetch next page resources
    const prefetchResources = [
      '/about',
      '/contact',
      '/services'
    ]
    
    // Prefetch after initial load
    setTimeout(() => {
      prefetchResources.forEach(href => {
        const link = document.createElement('link')
        link.rel = 'prefetch'
        link.href = href
        document.head.appendChild(link)
      })
    }, 2000)
  }, [])
  
  return null
}

/**
 * Performance Observer Component
 * Monitors Core Web Vitals and sends metrics
 */
export const PerformanceObserver: React.FC = () => {
  useEffect(() => {
    // Only run in production
    if (process.env.NODE_ENV !== 'production') return
    
    // Observe Core Web Vitals
    const observeWebVitals = () => {
      // LCP (Largest Contentful Paint)
      const lcpObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'largest-contentful-paint') {
            console.log('LCP:', entry.startTime)
            // Send to analytics
            // gtag('event', 'web_vitals', { name: 'LCP', value: entry.startTime })
          }
        })
      })
      
      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
      } catch (e) {
        // Browser doesn't support LCP
      }
      
      // FID (First Input Delay)
      const fidObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'first-input') {
            const fid = entry.processingStart - entry.startTime
            console.log('FID:', fid)
            // Send to analytics
            // gtag('event', 'web_vitals', { name: 'FID', value: fid })
          }
        })
      })
      
      try {
        fidObserver.observe({ entryTypes: ['first-input'] })
      } catch (e) {
        // Browser doesn't support FID
      }
      
      // CLS (Cumulative Layout Shift)
      let clsValue = 0
      const clsObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
            clsValue += entry.value
            console.log('CLS:', clsValue)
            // Send to analytics
            // gtag('event', 'web_vitals', { name: 'CLS', value: clsValue })
          }
        })
      })
      
      try {
        clsObserver.observe({ entryTypes: ['layout-shift'] })
      } catch (e) {
        // Browser doesn't support CLS
      }
    }
    
    // Start observing after page load
    if (document.readyState === 'complete') {
      observeWebVitals()
    } else {
      window.addEventListener('load', observeWebVitals)
    }
    
    return () => {
      window.removeEventListener('load', observeWebVitals)
    }
  }, [])
  
  return null
}

/**
 * Service Worker Registration Component
 * Registers service worker for PWA functionality
 */
export const ServiceWorkerRegistration: React.FC = () => {
  useEffect(() => {
    // Only register in production
    if (process.env.NODE_ENV !== 'production') return
    
    // Register service worker
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', async () => {
        try {
          const registration = await navigator.serviceWorker.register('/sw.js', {
            scope: '/',
            updateViaCache: 'none'
          })
          
          console.log('SW registered:', registration)
          
          // Listen for updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  // New content available, show update notification
                  console.log('New content available, please refresh.')
                }
              })
            }
          })
        } catch (error) {
          console.log('SW registration failed:', error)
        }
      })
    }
  }, [])
  
  return null
}

export default CriticalCSS
