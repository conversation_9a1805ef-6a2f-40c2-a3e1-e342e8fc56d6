/**
 * ATLAS v2.4 - Contact Form Hook
 * Extracted from ContactForm.tsx for better testability and reusability
 */

import { useState, useRef, useEffect, useCallback } from 'react'
import emailjs from '@emailjs/browser'

// Types
export interface ContactFormData {
  name: string
  email: string
  company: string
  phone: string
  message: string
  service: string
}

export interface FormStatus {
  type: 'success' | 'error' | null
  message: string
}

export interface ContactFormConfig {
  serviceId: string
  templateId: string
  publicKey: string
  enableLogs?: boolean
}

// Default configuration
const DEFAULT_CONFIG: ContactFormConfig = {
  serviceId: 'service_1k212a9',
  templateId: 'template_93m0kce',
  publicKey: 'NuEMLaMO5zEqU4ka1',
  enableLogs: process.env.NODE_ENV === 'development',
}

/**
 * Custom hook for contact form management
 * Handles form state, validation, submission, and EmailJS integration
 */
export const useContactForm = (config: Partial<ContactFormConfig> = {}) => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config }
  
  // Form reference
  const formRef = useRef<HTMLFormElement>(null)
  
  // Form state
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    email: '',
    company: '',
    phone: '',
    message: '',
    service: '',
  })
  
  const [formStatus, setFormStatus] = useState<FormStatus>({
    type: null,
    message: '',
  })
  
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [logs, setLogs] = useState<string[]>([])
  
  // Logging utility
  const addLog = useCallback((message: string) => {
    if (!finalConfig.enableLogs) return
    
    const timestamp = new Date().toLocaleTimeString()
    const logMessage = `[${timestamp}] ${message}`
    setLogs(prev => [...prev, logMessage])
    console.log(logMessage)
  }, [finalConfig.enableLogs])
  
  // Initialize EmailJS
  useEffect(() => {
    emailjs.init(finalConfig.publicKey)
    addLog('✅ EmailJS inicializado correctamente')
  }, [finalConfig.publicKey, addLog])
  
  // Form validation
  const validateForm = useCallback((data: ContactFormData): string | null => {
    if (!data.name.trim()) {
      return 'El nombre es requerido'
    }
    
    if (!data.email.trim()) {
      return 'El email es requerido'
    }
    
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(data.email)) {
      return 'El email no tiene un formato válido'
    }
    
    if (!data.message.trim()) {
      return 'El mensaje es requerido'
    }
    
    if (data.message.trim().length < 10) {
      return 'El mensaje debe tener al menos 10 caracteres'
    }
    
    return null
  }, [])
  
  // Handle form field changes
  const handleChange = useCallback((
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target
    
    // Map field names for internal state
    const stateField = name === 'user_name' ? 'name' :
                       name === 'user_email' ? 'email' : name
    
    addLog(`📝 Campo ${name} cambiado a: ${value}`)
    
    setFormData(prev => ({
      ...prev,
      [stateField]: value,
    }))
    
    // Clear error status when user starts typing
    if (formStatus.type === 'error') {
      setFormStatus({ type: null, message: '' })
    }
  }, [addLog, formStatus.type])
  
  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault()
    addLog('📝 Formulario enviado')
    
    // Clear previous messages
    setFormStatus({ type: null, message: '' })
    
    // Validate form
    const validationError = validateForm(formData)
    if (validationError) {
      addLog(`❌ Validación fallida: ${validationError}`)
      setFormStatus({
        type: 'error',
        message: validationError,
      })
      return
    }
    
    setIsSubmitting(true)
    addLog('📧 Iniciando envío de email con EmailJS...')
    addLog(`📋 Datos: ${JSON.stringify(formData, null, 2)}`)
    
    try {
      // Send email using EmailJS
      const result = await emailjs.sendForm(
        finalConfig.serviceId,
        finalConfig.templateId,
        formRef.current!,
        finalConfig.publicKey
      )
      
      addLog('✅ Email enviado exitosamente')
      addLog(`📊 Resultado: ${JSON.stringify(result, null, 2)}`)
      
      setFormStatus({
        type: 'success',
        message: '¡Gracias por tu mensaje! Nos pondremos en contacto contigo pronto.',
      })
      
      // Reset form
      resetForm()
      
    } catch (error: any) {
      addLog(`❌ Error al enviar email: ${JSON.stringify(error, null, 2)}`)
      
      let errorMessage = 'Hubo un error al enviar el mensaje. Por favor, inténtalo de nuevo.'
      
      if (error.text) {
        errorMessage = `Error: ${error.text}`
      } else if (error.message) {
        errorMessage = `Error: ${error.message}`
      }
      
      setFormStatus({
        type: 'error',
        message: errorMessage,
      })
    } finally {
      addLog('🏁 Proceso de envío completado')
      setIsSubmitting(false)
    }
  }, [formData, validateForm, addLog, finalConfig])
  
  // Reset form
  const resetForm = useCallback(() => {
    setFormData({
      name: '',
      email: '',
      company: '',
      phone: '',
      message: '',
      service: '',
    })
    setFormStatus({ type: null, message: '' })
    addLog('🔄 Formulario reseteado')
  }, [addLog])
  
  // Clear status
  const clearStatus = useCallback(() => {
    setFormStatus({ type: null, message: '' })
  }, [])
  
  // Public API
  return {
    // Form state
    formData,
    formStatus,
    isSubmitting,
    logs,
    
    // Form reference
    formRef,
    
    // Form handlers
    handleChange,
    handleSubmit,
    resetForm,
    clearStatus,
    
    // Utilities
    validateForm,
    
    // Computed values
    isValid: validateForm(formData) === null,
    hasChanges: Object.values(formData).some(value => value.trim() !== ''),
  }
}

export default useContactForm
