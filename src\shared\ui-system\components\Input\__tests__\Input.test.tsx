import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ThemeProvider } from '@/context/ThemeContext'
import { Search, User } from 'lucide-react'
import Input from '../Input'

// Test wrapper with theme context
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider>{children}</ThemeProvider>
)

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    label: ({ children, ...props }: any) => <label {...props}>{children}</label>,
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}))

describe('Input Component - ATLAS v2.4', () => {
  describe('Basic Rendering', () => {
    it('renders with default props', () => {
      render(
        <TestWrapper>
          <Input placeholder="Enter text" />
        </TestWrapper>
      )
      
      const input = screen.getByPlaceholderText('Enter text')
      expect(input).toBeInTheDocument()
      expect(input).toHaveAttribute('type', 'text')
    })

    it('renders with label', () => {
      render(
        <TestWrapper>
          <Input label="Username" />
        </TestWrapper>
      )
      
      expect(screen.getByText('Username')).toBeInTheDocument()
      const input = screen.getByRole('textbox')
      expect(input).toHaveAttribute('id')
    })

    it('renders with helper text', () => {
      render(
        <TestWrapper>
          <Input helperText="Enter your username" />
        </TestWrapper>
      )
      
      expect(screen.getByText('Enter your username')).toBeInTheDocument()
    })
  })

  describe('Variants', () => {
    const variants = ['default', 'filled', 'outlined', 'underlined'] as const

    variants.forEach(variant => {
      it(`renders ${variant} variant correctly`, () => {
        render(
          <TestWrapper>
            <Input variant={variant} placeholder={`${variant} input`} />
          </TestWrapper>
        )
        
        expect(screen.getByPlaceholderText(`${variant} input`)).toBeInTheDocument()
      })
    })
  })

  describe('Sizes', () => {
    const sizes = ['sm', 'md', 'lg'] as const

    sizes.forEach(size => {
      it(`renders ${size} size correctly`, () => {
        render(
          <TestWrapper>
            <Input size={size} placeholder={`${size} input`} />
          </TestWrapper>
        )
        
        expect(screen.getByPlaceholderText(`${size} input`)).toBeInTheDocument()
      })
    })
  })

  describe('States', () => {
    it('renders error state', () => {
      render(
        <TestWrapper>
          <Input error errorMessage="This field is required" />
        </TestWrapper>
      )
      
      expect(screen.getByText('This field is required')).toBeInTheDocument()
      // Check for error icon
      const errorIcon = document.querySelector('[data-testid="alert-circle"]')
      expect(errorIcon || screen.getByText('This field is required')).toBeInTheDocument()
    })

    it('renders success state', () => {
      render(
        <TestWrapper>
          <Input success />
        </TestWrapper>
      )
      
      // Check for success icon
      const successIcon = document.querySelector('[data-testid="check-circle"]')
      expect(successIcon || screen.getByRole('textbox')).toBeInTheDocument()
    })

    it('renders loading state', () => {
      render(
        <TestWrapper>
          <Input loading />
        </TestWrapper>
      )
      
      // Check for loading spinner
      const spinner = document.querySelector('.border-t-transparent')
      expect(spinner).toBeInTheDocument()
    })

    it('renders disabled state', () => {
      render(
        <TestWrapper>
          <Input disabled placeholder="Disabled input" />
        </TestWrapper>
      )
      
      const input = screen.getByPlaceholderText('Disabled input')
      expect(input).toBeDisabled()
    })
  })

  describe('Icons and Addons', () => {
    it('renders with left icon', () => {
      render(
        <TestWrapper>
          <Input leftIcon={<User data-testid="user-icon" />} />
        </TestWrapper>
      )
      
      expect(screen.getByTestId('user-icon')).toBeInTheDocument()
    })

    it('renders with right icon', () => {
      render(
        <TestWrapper>
          <Input rightIcon={<Search data-testid="search-icon" />} />
        </TestWrapper>
      )
      
      expect(screen.getByTestId('search-icon')).toBeInTheDocument()
    })

    it('renders with left addon', () => {
      render(
        <TestWrapper>
          <Input leftAddon={<span>$</span>} />
        </TestWrapper>
      )
      
      expect(screen.getByText('$')).toBeInTheDocument()
    })

    it('renders with right addon', () => {
      render(
        <TestWrapper>
          <Input rightAddon={<span>.com</span>} />
        </TestWrapper>
      )
      
      expect(screen.getByText('.com')).toBeInTheDocument()
    })
  })

  describe('Password Toggle', () => {
    it('shows password toggle for password input', () => {
      render(
        <TestWrapper>
          <Input type="password" showPasswordToggle />
        </TestWrapper>
      )
      
      const input = screen.getByRole('textbox')
      expect(input).toHaveAttribute('type', 'password')
      
      const toggleButton = screen.getByLabelText('Show password')
      expect(toggleButton).toBeInTheDocument()
    })

    it('toggles password visibility', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Input type="password" showPasswordToggle />
        </TestWrapper>
      )
      
      const input = screen.getByRole('textbox')
      const toggleButton = screen.getByLabelText('Show password')
      
      expect(input).toHaveAttribute('type', 'password')
      
      await user.click(toggleButton)
      expect(input).toHaveAttribute('type', 'text')
      
      const hideButton = screen.getByLabelText('Hide password')
      await user.click(hideButton)
      expect(input).toHaveAttribute('type', 'password')
    })
  })

  describe('Clearable Input', () => {
    it('shows clear button when input has value', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Input clearable />
        </TestWrapper>
      )
      
      const input = screen.getByRole('textbox')
      await user.type(input, 'test value')
      
      const clearButton = screen.getByLabelText('Clear input')
      expect(clearButton).toBeInTheDocument()
    })

    it('clears input when clear button is clicked', async () => {
      const user = userEvent.setup()
      const handleChange = jest.fn()
      
      render(
        <TestWrapper>
          <Input clearable onChange={handleChange} />
        </TestWrapper>
      )
      
      const input = screen.getByRole('textbox')
      await user.type(input, 'test')
      
      const clearButton = screen.getByLabelText('Clear input')
      await user.click(clearButton)
      
      expect(handleChange).toHaveBeenLastCalledWith(
        expect.objectContaining({
          target: expect.objectContaining({ value: '' })
        })
      )
    })
  })

  describe('Form Integration', () => {
    it('handles value changes', async () => {
      const handleChange = jest.fn()
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Input onChange={handleChange} />
        </TestWrapper>
      )
      
      const input = screen.getByRole('textbox')
      await user.type(input, 'test')
      
      expect(handleChange).toHaveBeenCalledTimes(4) // One for each character
    })

    it('handles focus and blur events', async () => {
      const handleFocus = jest.fn()
      const handleBlur = jest.fn()
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Input onFocus={handleFocus} onBlur={handleBlur} />
        </TestWrapper>
      )
      
      const input = screen.getByRole('textbox')
      
      await user.click(input)
      expect(handleFocus).toHaveBeenCalledTimes(1)
      
      await user.tab()
      expect(handleBlur).toHaveBeenCalledTimes(1)
    })

    it('supports required validation', () => {
      render(
        <TestWrapper>
          <Input label="Required Field" required />
        </TestWrapper>
      )
      
      const input = screen.getByRole('textbox')
      expect(input).toHaveAttribute('required')
      expect(screen.getByText('*')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('has proper label association', () => {
      render(
        <TestWrapper>
          <Input label="Username" />
        </TestWrapper>
      )
      
      const input = screen.getByRole('textbox')
      const label = screen.getByText('Username')
      
      expect(input).toHaveAttribute('id')
      expect(label).toHaveAttribute('for', input.getAttribute('id'))
    })

    it('supports custom ARIA attributes', () => {
      render(
        <TestWrapper>
          <Input aria-describedby="help-text" />
        </TestWrapper>
      )
      
      const input = screen.getByRole('textbox')
      expect(input).toHaveAttribute('aria-describedby', 'help-text')
    })

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Input />
        </TestWrapper>
      )
      
      const input = screen.getByRole('textbox')
      
      await user.tab()
      expect(input).toHaveFocus()
    })
  })

  describe('Theme Integration', () => {
    it('adapts to theme changes', () => {
      render(
        <TestWrapper>
          <Input placeholder="Themed input" />
        </TestWrapper>
      )
      
      expect(screen.getByPlaceholderText('Themed input')).toBeInTheDocument()
    })
  })

  describe('Performance', () => {
    it('renders efficiently without unnecessary re-renders', () => {
      const { rerender } = render(
        <TestWrapper>
          <Input placeholder="Test input" />
        </TestWrapper>
      )
      
      rerender(
        <TestWrapper>
          <Input placeholder="Test input" />
        </TestWrapper>
      )
      
      expect(screen.getByPlaceholderText('Test input')).toBeInTheDocument()
    })
  })
})
