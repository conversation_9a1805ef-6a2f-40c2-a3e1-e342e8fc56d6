#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# ATLAS v2.4 Pre-commit Hook
echo "🚀 ATLAS v2.4 - Running pre-commit checks..."

# Run lint-staged for staged files
npx lint-staged

# Run type checking
echo "🔍 Type checking..."
npm run type-check

# Check if there are any TypeScript errors
if [ $? -ne 0 ]; then
  echo "❌ TypeScript errors found. Please fix them before committing."
  exit 1
fi

echo "✅ All pre-commit checks passed!"
