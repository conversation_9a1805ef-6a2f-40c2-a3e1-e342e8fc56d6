<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="200" viewBox="0 0 600 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Definición de gradientes para el logo -->
  <defs>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#3b82f6" /> <!-- blue-500 -->
      <stop offset="50%" stop-color="#38bdf8" /> <!-- sky-400 -->
      <stop offset="100%" stop-color="#14b8a6" /> <!-- teal-500 -->
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3b82f6" /> <!-- blue-500 -->
      <stop offset="100%" stop-color="#14b8a6" /> <!-- teal-500 -->
    </linearGradient>
  </defs>

  <!-- Modern AI Icon - Abstract Geometric Design -->
  <g transform="translate(40, 40)">
    <!-- Base geometric shapes -->
    <rect x="60" y="35" width="60" height="60" rx="5" fill="url(#iconGradient)" transform="rotate(45 90 65)"/>
    <circle cx="90" cy="65" r="30" fill="none" stroke="#ffffff" stroke-width="3"/>
    
    <!-- Dynamic elements -->
    <path d="M90,35 L90,95" stroke="#ffffff" stroke-width="2.5" stroke-dasharray="4,4"/>
    <path d="M60,65 L120,65" stroke="#ffffff" stroke-width="2.5" stroke-dasharray="4,4"/>
    
    <!-- Connection points -->
    <circle cx="90" cy="35" r="3" fill="#ffffff"/>
    <circle cx="90" cy="95" r="3" fill="#ffffff"/>
    <circle cx="60" cy="65" r="3" fill="#ffffff"/>
    <circle cx="120" cy="65" r="3" fill="#ffffff"/>
    
    <!-- Animated pulse effect -->
    <circle cx="90" cy="65" r="40" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.3">
      <animate attributeName="r" values="40;50;40" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.3;0;0.3" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Text with modern typography and proper spacing -->
  <g>
    <text x="190" y="120" font-family="Arial, sans-serif" font-size="62" font-weight="bold" fill="#0f172a">InformatiK</text>
    <text x="500" y="120" font-family="Arial, sans-serif" font-size="62" font-weight="bold" fill="#0f172a">-</text>
    <text x="530" y="120" font-family="Arial, sans-serif" font-size="62" font-weight="bold" fill="url(#textGradient)">AI</text>
  </g>
</svg>
