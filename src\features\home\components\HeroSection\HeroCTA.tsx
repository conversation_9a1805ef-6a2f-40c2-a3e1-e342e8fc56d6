'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Button } from '@/shared/ui-system'

interface HeroCTAProps {
  className?: string
}

/**
 * Hero Call-to-Action component
 * Contains the main action buttons for the hero section
 */
const HeroCTA: React.FC<HeroCTAProps> = ({ className = '' }) => {

  const animationVariants = {
    fadeInUp: {
      hidden: { opacity: 0, y: 30 },
      visible: {
        opacity: 1,
        y: 0,
        transition: { duration: 0.6, ease: [0.22, 1, 0.36, 1] },
      },
    },
  }

  return (
    <motion.div
      className={`flex flex-col sm:flex-row gap-5 sm:gap-6 justify-center items-center ${className}`}
      variants={animationVariants.fadeInUp}
      initial="hidden"
      animate="visible"
      transition={{ delay: 0.6 }}
    >
      {/* Primary CTA Button */}
      <motion.div
        className="w-full sm:w-auto"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Button
          href="/services"
          variant="gradient"
          size="lg"
          effect="particles"
          tooltip="Descubre nuestros servicios de IA"
          className="w-full sm:w-auto text-base sm:text-lg md:text-xl font-bold"
        >
          Explorar Nuestros Servicios
        </Button>
      </motion.div>

      {/* Secondary CTA Button */}
      <motion.div
        className="w-full sm:w-auto"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Button
          href="/contact"
          variant="outline"
          size="lg"
          effect="glow"
          tooltip="Contáctanos para una consulta gratuita"
          className="w-full sm:w-auto text-base sm:text-lg md:text-xl font-bold backdrop-blur-sm"
        >
          Consulta Gratuita
        </Button>
      </motion.div>
    </motion.div>
  )
}

export default HeroCTA
