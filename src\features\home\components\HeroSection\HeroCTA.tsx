'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import ButtonWithEffect from '@/components/ui/ButtonWithEffect'

interface HeroCTAProps {
  className?: string
}

/**
 * Hero Call-to-Action component
 * Contains the main action buttons for the hero section
 */
const HeroCTA: React.FC<HeroCTAProps> = ({ className = '' }) => {
  const [isButtonHovered, setIsButtonHovered] = useState(false)

  const animationVariants = {
    fadeInUp: {
      hidden: { opacity: 0, y: 30 },
      visible: {
        opacity: 1,
        y: 0,
        transition: { duration: 0.6, ease: [0.22, 1, 0.36, 1] },
      },
    },
  }

  return (
    <motion.div
      className={`flex flex-col sm:flex-row gap-5 sm:gap-6 justify-center items-center ${className}`}
      variants={animationVariants.fadeInUp}
      initial="hidden"
      animate="visible"
      transition={{ delay: 0.6 }}
    >
      {/* Primary CTA Button */}
      <motion.div
        className="w-full sm:w-auto"
        onMouseEnter={() => setIsButtonHovered(true)}
        onMouseLeave={() => setIsButtonHovered(false)}
      >
        <ButtonWithEffect
          href="/services"
          size="lg"
          effectType="particles"
          tooltip="Descubre nuestros servicios de IA"
          className="bg-gradient-to-r from-[#0ea5e9] to-[#14b8a6] border-0 px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg md:text-xl font-bold w-full sm:w-auto text-white"
        >
          Explorar Nuestros Servicios
        </ButtonWithEffect>
      </motion.div>

      {/* Secondary CTA Button */}
      <motion.div
        className="w-full sm:w-auto"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <ButtonWithEffect
          href="/contact"
          variant="outline"
          size="lg"
          effectType="glow"
          tooltip="Contáctanos para una consulta gratuita"
          className="border-2 border-[#00F0FF]/50 text-[#00F0FF] hover:bg-[#00F0FF]/10 px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg md:text-xl font-bold w-full sm:w-auto backdrop-blur-sm"
        >
          Consulta Gratuita
        </ButtonWithEffect>
      </motion.div>
    </motion.div>
  )
}

export default HeroCTA
