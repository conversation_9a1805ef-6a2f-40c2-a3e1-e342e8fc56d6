'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react'
import { useThemeStyles } from '@/shared/ui-system'
import type { FormStatus as FormStatusType } from '../../hooks/useContactForm'

interface FormStatusProps {
  status: FormStatusType
  className?: string
}

/**
 * Form Status Component
 * Displays success, error, or info messages with animations
 * Extracted from ContactForm for better modularity
 */
const FormStatus: React.FC<FormStatusProps> = ({ status, className = '' }) => {
  const themeStyles = useThemeStyles()
  
  // Don't render if no status
  if (!status.type || !status.message) {
    return null
  }
  
  // Get status-specific styles and icons
  const getStatusConfig = () => {
    switch (status.type) {
      case 'success':
        return {
          icon: CheckCircle,
          bgColor: themeStyles.utils.isDark 
            ? 'bg-green-900/20 border-green-500/30' 
            : 'bg-green-50 border-green-200',
          textColor: themeStyles.utils.isDark 
            ? 'text-green-400' 
            : 'text-green-700',
          iconColor: themeStyles.utils.isDark 
            ? 'text-green-400' 
            : 'text-green-500',
        }
      
      case 'error':
        return {
          icon: XCircle,
          bgColor: themeStyles.utils.isDark 
            ? 'bg-red-900/20 border-red-500/30' 
            : 'bg-red-50 border-red-200',
          textColor: themeStyles.utils.isDark 
            ? 'text-red-400' 
            : 'text-red-700',
          iconColor: themeStyles.utils.isDark 
            ? 'text-red-400' 
            : 'text-red-500',
        }
      
      default:
        return {
          icon: AlertCircle,
          bgColor: themeStyles.utils.isDark 
            ? 'bg-blue-900/20 border-blue-500/30' 
            : 'bg-blue-50 border-blue-200',
          textColor: themeStyles.utils.isDark 
            ? 'text-blue-400' 
            : 'text-blue-700',
          iconColor: themeStyles.utils.isDark 
            ? 'text-blue-400' 
            : 'text-blue-500',
        }
    }
  }
  
  const config = getStatusConfig()
  const Icon = config.icon
  
  // Animation variants
  const statusVariants = {
    hidden: {
      opacity: 0,
      y: -10,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.3,
        ease: 'easeOut',
      },
    },
    exit: {
      opacity: 0,
      y: -10,
      scale: 0.95,
      transition: {
        duration: 0.2,
        ease: 'easeIn',
      },
    },
  }
  
  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={`${status.type}-${status.message}`}
        className={`
          flex items-start space-x-3 p-4 rounded-lg border
          ${config.bgColor}
          ${className}
        `}
        variants={statusVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        role="alert"
        aria-live="polite"
      >
        {/* Status Icon */}
        <div className="flex-shrink-0">
          <Icon 
            className={`w-5 h-5 ${config.iconColor}`}
            aria-hidden="true"
          />
        </div>
        
        {/* Status Message */}
        <div className="flex-1">
          <p className={`text-sm font-medium ${config.textColor}`}>
            {status.message}
          </p>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}

export default FormStatus
