'use client'

import React from 'react'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { useThemeStyles } from '@/shared/ui-system'

interface CompanyHistoryProps {
  className?: string
}

/**
 * Company History Section Component
 * Extracted from about/page.tsx for better modularity
 */
const CompanyHistory: React.FC<CompanyHistoryProps> = ({ className = '' }) => {
  const themeStyles = useThemeStyles()
  
  // Animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' },
    },
  }
  
  const slideInLeft = {
    hidden: { opacity: 0, x: -50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.8, ease: 'easeOut' },
    },
  }
  
  const slideInRight = {
    hidden: { opacity: 0, x: 50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.8, ease: 'easeOut' },
    },
  }
  
  return (
    <section className={`py-20 ${themeStyles.background.secondary} relative overflow-hidden ${className}`}>
      {/* Background effects */}
      <div className="absolute bottom-0 right-1/4 w-1/2 h-1/3 rounded-full filter blur-[120px] bg-[#00B4DB]/10" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Text Content */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: '-100px' }}
            variants={slideInLeft}
          >
            <div className="mb-8">
              <motion.h2 
                className="text-4xl font-bold mb-4 text-glow"
                variants={fadeInUp}
              >
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-blue-300 to-blue-200">
                  Nuestra Historia
                </span>
              </motion.h2>
              <motion.p 
                className={`${themeStyles.brand.primary} text-xl`}
                variants={fadeInUp}
              >
                Cómo comenzó Informatik-AI y hacia dónde nos dirigimos
              </motion.p>
            </div>
            
            <motion.div className="space-y-4" variants={fadeInUp}>
              <p className={themeStyles.text.secondary}>
                Fundada en 2020, Informatik-AI nació de la visión de hacer que
                las tecnologías avanzadas de IA fueran accesibles para empresas
                de todos los tamaños. Nuestros fundadores, un equipo de
                ingenieros y científicos de datos con experiencia en las
                principales empresas tecnológicas del mundo, identificaron una
                brecha significativa en el mercado.
              </p>
              
              <p className={themeStyles.text.secondary}>
                Mientras que las grandes corporaciones tenían acceso a equipos
                internos de IA y recursos ilimitados, las pequeñas y medianas
                empresas luchaban por implementar soluciones de inteligencia
                artificial que pudieran transformar realmente sus operaciones.
                Esta disparidad nos motivó a crear una empresa que democratizara
                el acceso a la IA.
              </p>
              
              <p className={themeStyles.text.secondary}>
                Desde entonces, hemos crecido de un pequeño equipo de 3 personas
                a una empresa de más de 50 especialistas en IA, trabajando con
                clientes en más de 15 países. Nuestro enfoque sigue siendo el
                mismo: proporcionar soluciones de IA de clase mundial con un
                compromiso de ayudar a las empresas a aprovechar el poder
                transformador de la IA para lograr sus objetivos.
              </p>
            </motion.div>
          </motion.div>
          
          {/* Image */}
          <motion.div
            className="relative h-96 rounded-xl overflow-hidden shadow-lg group"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: '-100px' }}
            variants={slideInRight}
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.3 }}
          >
            <div className="absolute inset-0">
              <Image
                src="/images/nosotros/fotoEquipo.jpg"
                alt="Equipo de Informatik-AI"
                fill
                style={{ objectFit: 'cover' }}
                className="rounded-xl transition-transform duration-500 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
            </div>
            
            {/* Overlay content */}
            <div className="absolute bottom-6 left-6 right-6">
              <h3 className="text-white text-xl font-bold mb-2">
                Nuestro Equipo
              </h3>
              <p className="text-gray-200 text-sm">
                Más de 50 especialistas trabajando juntos para transformar el futuro
              </p>
            </div>
            
            {/* Decorative elements */}
            <div className="absolute top-4 right-4 w-12 h-12 border-2 border-white/30 rounded-full flex items-center justify-center">
              <div className="w-6 h-6 bg-gradient-to-r from-[#00B4DB] to-[#48D1CC] rounded-full" />
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default CompanyHistory
