import type { <PERSON>a, StoryObj } from '@storybook/react'

import HeroSectionRefactored from './HeroSectionRefactored'
import { ThemeProvider } from '@/context/ThemeContext'

const meta: Meta<typeof HeroSectionRefactored> = {
  title: 'Features/Home/HeroSection',
  component: HeroSectionRefactored,
  decorators: [
    (Story) => (
      <ThemeProvider>
        <div className="min-h-screen">
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'Refactored HeroSection component following ATLAS v2.4 principles. Features modular architecture, typing animation, and responsive design.',
      },
    },
  },
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

// Default story
export const Default: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Default HeroSection with typing animation and responsive design.',
      },
    },
  },
}

// Mobile view
export const Mobile: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
    docs: {
      description: {
        story: 'HeroSection optimized for mobile devices with shorter phrases.',
      },
    },
  },
}

// Tablet view
export const Tablet: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'tablet',
    },
    docs: {
      description: {
        story: 'HeroSection on tablet devices.',
      },
    },
  },
}

// Desktop view
export const Desktop: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'desktop',
    },
    docs: {
      description: {
        story: 'HeroSection on desktop with full phrases and animations.',
      },
    },
  },
}

// Dark theme
export const DarkTheme: Story = {
  decorators: [
    (Story) => (
      <ThemeProvider>
        <div className="dark min-h-screen">
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    backgrounds: {
      default: 'dark',
    },
    docs: {
      description: {
        story: 'HeroSection in dark theme mode.',
      },
    },
  },
}

// Light theme
export const LightTheme: Story = {
  decorators: [
    (Story) => (
      <ThemeProvider>
        <div className="min-h-screen bg-[#e0fbff]">
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    backgrounds: {
      default: 'light',
    },
    docs: {
      description: {
        story: 'HeroSection in light theme mode.',
      },
    },
  },
}

// Performance test - no animations
export const NoAnimations: Story = {
  decorators: [
    (Story) => (
      <ThemeProvider>
        <div className="min-h-screen" style={{ '--motion-reduce': 'reduce' }}>
          <Story />
        </div>
      </ThemeProvider>
    ),
  ],
  parameters: {
    docs: {
      description: {
        story: 'HeroSection with reduced motion for accessibility and performance testing.',
      },
    },
  },
}

// Component showcase - individual components
export const ComponentShowcase: Story = {
  render: () => (
    <ThemeProvider>
      <div className="min-h-screen bg-gray-900 p-8 space-y-8">
        <div className="text-white text-2xl font-bold mb-8">
          HeroSection Components Showcase
        </div>
        
        {/* Individual components would be shown here */}
        <div className="grid gap-8">
          <div className="border border-gray-700 p-6 rounded-lg">
            <h3 className="text-white text-lg mb-4">Full HeroSection</h3>
            <div className="scale-50 origin-top-left w-[200%]">
              <HeroSectionRefactored />
            </div>
          </div>
        </div>
      </div>
    </ThemeProvider>
  ),
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        story: 'Showcase of HeroSection components for development and testing.',
      },
    },
  },
}
