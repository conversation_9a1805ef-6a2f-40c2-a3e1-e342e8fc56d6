name: ATLAS v2.4 CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  CACHE_KEY_PREFIX: 'atlas-v2.4'

jobs:
  # Quality Gates - Parallel execution for speed
  quality-gates:
    name: Quality Gates
    runs-on: ubuntu-latest
    strategy:
      matrix:
        gate: [
          'design-system',
          'bundle-size', 
          'accessibility',
          'performance',
          'code-quality',
          'unit-tests'
        ]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: |
          npm ci --prefer-offline --no-audit
          npm run postinstall || true
      
      - name: Build project
        run: npm run build
        env:
          NODE_ENV: production
          GENERATE_SOURCEMAP: false
      
      - name: Run Quality Gate - ${{ matrix.gate }}
        run: |
          case "${{ matrix.gate }}" in
            "design-system")
              npm run validate:design-system
              ;;
            "bundle-size")
              npm run validate:bundle-size
              ;;
            "accessibility")
              npm run validate:accessibility
              ;;
            "performance")
              npm run validate:performance
              ;;
            "code-quality")
              npm run quality
              ;;
            "unit-tests")
              npm run test:ci
              ;;
          esac
      
      - name: Upload Quality Gate Report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: quality-gate-${{ matrix.gate }}-report
          path: reports/
          retention-days: 30
  
  # E2E Tests
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: quality-gates
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit
      
      - name: Install Playwright browsers
        run: npx playwright install --with-deps
      
      - name: Build project
        run: npm run build
      
      - name: Start application
        run: npm start &
        env:
          PORT: 3000
      
      - name: Wait for application
        run: npx wait-on http://localhost:3000 --timeout 60000
      
      - name: Run E2E tests
        run: npm run test:e2e
        env:
          BASE_URL: http://localhost:3000
      
      - name: Upload E2E test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: e2e-test-results
          path: |
            test-results/
            playwright-report/
          retention-days: 30
  
  # Lighthouse Audit
  lighthouse-audit:
    name: Lighthouse Audit
    runs-on: ubuntu-latest
    needs: quality-gates
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: |
          npm ci --prefer-offline --no-audit
          npm install -g lighthouse chrome-launcher
      
      - name: Build project
        run: npm run build
      
      - name: Start application
        run: npm start &
        env:
          PORT: 3000
      
      - name: Wait for application
        run: npx wait-on http://localhost:3000 --timeout 60000
      
      - name: Run Lighthouse audit
        run: npm run lighthouse:audit
        env:
          BASE_URL: http://localhost:3000
      
      - name: Upload Lighthouse reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: lighthouse-reports
          path: reports/lighthouse-*.json
          retention-days: 30
  
  # Security Scan
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit
      
      - name: Run security audit
        run: |
          npm audit --audit-level=moderate
          npx audit-ci --moderate
      
      - name: Run dependency check
        run: |
          npx depcheck
          npx license-checker --summary
      
      - name: CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          languages: javascript
  
  # Build and Deploy (Production)
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [quality-gates, e2e-tests, lighthouse-audit, security-scan]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit
      
      - name: Run final quality gates
        run: npm run atlas:quality-gates
      
      - name: Build for production
        run: npm run build
        env:
          NODE_ENV: production
          GENERATE_SOURCEMAP: false
      
      - name: Run production tests
        run: |
          npm run test:production || true
          npm run validate:production || true
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          working-directory: ./
      
      - name: Post-deployment validation
        run: |
          sleep 30  # Wait for deployment to be ready
          npm run validate:deployment || true
        env:
          PRODUCTION_URL: ${{ secrets.PRODUCTION_URL }}
      
      - name: Notify deployment success
        if: success()
        run: |
          echo "🚀 ATLAS v2.4 deployed successfully to production!"
          echo "URL: ${{ secrets.PRODUCTION_URL }}"
  
  # Build and Deploy (Staging)
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [quality-gates, e2e-tests]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit
      
      - name: Build for staging
        run: npm run build
        env:
          NODE_ENV: production
          NEXT_PUBLIC_ENV: staging
      
      - name: Deploy to Vercel (Preview)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./
  
  # Generate Final Report
  generate-report:
    name: Generate Final Report
    runs-on: ubuntu-latest
    needs: [quality-gates, e2e-tests, lighthouse-audit, security-scan]
    if: always()
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts/
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit
      
      - name: Generate comprehensive report
        run: |
          node scripts/generate-ci-report.js
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          RUN_ID: ${{ github.run_id }}
      
      - name: Upload final report
        uses: actions/upload-artifact@v4
        with:
          name: atlas-v2.4-final-report
          path: reports/final-report.json
          retention-days: 90
      
      - name: Comment PR with results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const report = JSON.parse(fs.readFileSync('reports/final-report.json', 'utf8'));
            
            const comment = `
            ## 🏆 ATLAS v2.4 Quality Report
            
            **Overall Status**: ${report.overall_passed ? '✅ PASSED' : '❌ FAILED'}
            
            ### Quality Gates Results:
            ${Object.entries(report.quality_gates).map(([gate, result]) => 
              `- ${result.passed ? '✅' : '❌'} ${gate}: ${result.score || 'N/A'}`
            ).join('\n')}
            
            ### Performance Metrics:
            - **Bundle Size**: ${report.bundle_size || 'N/A'}
            - **Lighthouse Score**: ${report.lighthouse_score || 'N/A'}
            - **Test Coverage**: ${report.test_coverage || 'N/A'}
            
            ${report.recommendations?.length > 0 ? 
              `### Recommendations:\n${report.recommendations.map(r => `- ${r}`).join('\n')}` : 
              '### ✨ All quality standards met!'
            }
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

# Workflow notifications
  notify-completion:
    name: Notify Completion
    runs-on: ubuntu-latest
    needs: [deploy-production, deploy-staging, generate-report]
    if: always()
    
    steps:
      - name: Workflow Summary
        run: |
          echo "## 🚀 ATLAS v2.4 CI/CD Pipeline Completed" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Branch**: ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
          echo "**Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "**Trigger**: ${{ github.event_name }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Results:" >> $GITHUB_STEP_SUMMARY
          echo "- Quality Gates: ${{ needs.quality-gates.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- E2E Tests: ${{ needs.e2e-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Lighthouse Audit: ${{ needs.lighthouse-audit.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Security Scan: ${{ needs.security-scan.result }}" >> $GITHUB_STEP_SUMMARY
          
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "- Production Deploy: ${{ needs.deploy-production.result }}" >> $GITHUB_STEP_SUMMARY
          fi
          
          if [[ "${{ github.ref }}" == "refs/heads/develop" ]]; then
            echo "- Staging Deploy: ${{ needs.deploy-staging.result }}" >> $GITHUB_STEP_SUMMARY
          fi
