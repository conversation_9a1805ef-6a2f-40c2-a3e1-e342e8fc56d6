# Guía de Migración ATLAS v2.4

## Visión General

Esta guía proporciona instrucciones detalladas para migrar componentes legacy al Design System ATLAS v2.4, asegurando una transición suave y manteniendo la funcionalidad existente.

## 🎯 Objetivos de Migración

- **Consistencia**: Unificar todos los componentes bajo el design system
- **Performance**: Mejorar rendimiento con componentes optimizados
- **Mantenibilidad**: Reducir duplicación y simplificar mantenimiento
- **Accesibilidad**: Garantizar WCAG 2.1 AA compliance
- **Escalabilidad**: Preparar para crecimiento futuro

## 📋 Checklist de Migración

### Pre-Migración

- [ ] Identificar componentes legacy a migrar
- [ ] Analizar dependencias y uso actual
- [ ] Planificar estrategia de migración gradual
- [ ] Configurar entorno de testing
- [ ] Backup del código existente

### Durante la Migración

- [ ] Migrar componente por componente
- [ ] Mantener API compatibility cuando sea posible
- [ ] Actualizar tests existentes
- [ ] Validar funcionalidad en cada paso
- [ ] Documentar cambios realizados

### Post-Migración

- [ ] Ejecutar suite completa de tests
- [ ] Validar accesibilidad
- [ ] Verificar performance
- [ ] Actualizar documentación
- [ ] Eliminar código legacy

## 🔄 Patrones de Migración

### 1. Migración de Botones

#### Antes (Legacy)
```typescript
// components/ui/Button.tsx
const Button = ({ variant, size, children, ...props }) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'primary':
        return 'bg-blue-500 text-white hover:bg-blue-600'
      case 'secondary':
        return 'bg-gray-200 text-gray-800 hover:bg-gray-300'
      default:
        return 'bg-blue-500 text-white'
    }
  }
  
  return (
    <button 
      className={`px-4 py-2 rounded ${getVariantClasses()}`}
      {...props}
    >
      {children}
    </button>
  )
}
```

#### Después (ATLAS v2.4)
```typescript
// Importar del design system
import { Button } from '@/shared/ui-system'

// Uso directo
<Button variant="primary" size="md">
  Click me
</Button>

// O crear wrapper de compatibilidad si es necesario
const LegacyButton = (props) => {
  // Mapear props legacy a nuevas props
  const mappedProps = mapLegacyProps(props)
  return <Button {...mappedProps} />
}
```

### 2. Migración de Inputs

#### Antes (Legacy)
```typescript
const Input = ({ label, error, ...props }) => (
  <div>
    {label && <label>{label}</label>}
    <input 
      className={`border rounded px-3 py-2 ${error ? 'border-red-500' : 'border-gray-300'}`}
      {...props}
    />
    {error && <span className="text-red-500">{error}</span>}
  </div>
)
```

#### Después (ATLAS v2.4)
```typescript
import { Input } from '@/shared/ui-system'

<Input
  label="Email"
  error={!!errors.email}
  errorMessage={errors.email}
  variant="outlined"
  {...props}
/>
```

### 3. Migración de Cards

#### Antes (Legacy)
```typescript
const Card = ({ children, className }) => (
  <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
    {children}
  </div>
)
```

#### Después (ATLAS v2.4)
```typescript
import { Card } from '@/shared/ui-system'

<Card variant="elevated" padding="lg">
  {children}
</Card>
```

### 4. Migración de Modals

#### Antes (Legacy)
```typescript
const Modal = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
      <div className="bg-white rounded-lg p-6 max-w-md w-full">
        <button onClick={onClose} className="float-right">×</button>
        {children}
      </div>
    </div>
  )
}
```

#### Después (ATLAS v2.4)
```typescript
import { Modal } from '@/shared/ui-system'

<Modal
  isOpen={isOpen}
  onClose={onClose}
  title="Modal Title"
  size="md"
  variant="centered"
>
  {children}
</Modal>
```

## 🎨 Migración de Estilos

### 1. De CSS Hardcoded a Design Tokens

#### Antes
```css
.custom-button {
  background-color: #3B82F6;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
}
```

#### Después
```typescript
import { useDesignTokens } from '@/shared/ui-system'

const CustomButton = () => {
  const tokens = useDesignTokens()
  
  return (
    <button
      style={{
        backgroundColor: tokens.colors.primary[500],
        color: tokens.colors.white,
        padding: `${tokens.spacing[3]} ${tokens.spacing[6]}`,
        borderRadius: tokens.borderRadius.md
      }}
    >
      Button
    </button>
  )
}
```

### 2. De Clases CSS a Utility Classes

#### Antes
```css
.card-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 24px;
}
```

#### Después
```typescript
// Usando Tailwind con design tokens
<div className="bg-white rounded-xl shadow-lg p-6">
  Content
</div>

// O usando el componente Card
<Card variant="elevated" padding="lg">
  Content
</Card>
```

## 🔧 Herramientas de Migración

### Script Automatizado

```bash
# Ejecutar script de migración automática
npm run migrate:design-system

# Migrar componente específico
npm run migrate:component -- Button

# Validar migración
npm run validate:migration
```

### Codemod para Migración

```javascript
// codemod-migrate-button.js
module.exports = function transformer(fileInfo, api) {
  const j = api.jscodeshift
  
  return j(fileInfo.source)
    .find(j.ImportDeclaration, {
      source: { value: '@/components/ui/Button' }
    })
    .replaceWith(
      j.importDeclaration(
        [j.importSpecifier(j.identifier('Button'))],
        j.literal('@/shared/ui-system')
      )
    )
    .toSource()
}
```

### Validación Automática

```typescript
// validate-migration.ts
export function validateMigration(componentPath: string) {
  const checks = [
    checkImports,
    checkProps,
    checkAccessibility,
    checkPerformance
  ]
  
  return checks.every(check => check(componentPath))
}
```

## 📊 Tracking de Progreso

### Métricas de Migración

```typescript
// migration-progress.ts
export const migrationProgress = {
  total: 45,
  migrated: 42,
  remaining: 3,
  percentage: 93.3,
  
  byCategory: {
    buttons: { total: 8, migrated: 8, percentage: 100 },
    inputs: { total: 12, migrated: 11, percentage: 91.7 },
    cards: { total: 6, migrated: 6, percentage: 100 },
    modals: { total: 4, migrated: 4, percentage: 100 },
    others: { total: 15, migrated: 13, percentage: 86.7 }
  }
}
```

### Dashboard de Progreso

```bash
# Ver progreso de migración
npm run migration:status

# Generar reporte
npm run migration:report
```

## ⚠️ Problemas Comunes y Soluciones

### 1. Props Incompatibles

**Problema**: Props legacy no coinciden con nuevas props

**Solución**: Crear mapper de props
```typescript
const mapLegacyProps = (legacyProps) => {
  return {
    variant: legacyProps.type === 'primary' ? 'gradient' : 'secondary',
    size: legacyProps.large ? 'lg' : 'md',
    disabled: legacyProps.isDisabled,
    ...legacyProps
  }
}
```

### 2. Estilos Personalizados

**Problema**: Estilos CSS específicos que no existen en el design system

**Solución**: Extender componente o usar className
```typescript
<Button 
  variant="primary"
  className="custom-specific-styles"
>
  Custom Button
</Button>
```

### 3. Funcionalidad Específica

**Problema**: Funcionalidad custom que no está en el componente base

**Solución**: Crear wrapper que extienda funcionalidad
```typescript
const CustomButton = ({ onSpecialClick, ...props }) => {
  const handleClick = (e) => {
    // Lógica custom
    onSpecialClick?.(e)
    props.onClick?.(e)
  }
  
  return <Button {...props} onClick={handleClick} />
}
```

## 🧪 Testing Durante Migración

### 1. Tests de Regresión

```typescript
// regression.test.tsx
describe('Migration Regression Tests', () => {
  it('should maintain existing functionality', () => {
    // Test que la funcionalidad existente sigue funcionando
  })
  
  it('should improve accessibility', () => {
    // Test que la accesibilidad ha mejorado
  })
  
  it('should maintain visual consistency', () => {
    // Test visual regression
  })
})
```

### 2. Tests de Performance

```typescript
// performance.test.tsx
describe('Performance Tests', () => {
  it('should not degrade performance', () => {
    // Medir tiempo de render antes y después
  })
  
  it('should reduce bundle size', () => {
    // Verificar que el bundle es menor
  })
})
```

## 📚 Recursos Adicionales

### Documentación

- [Design System Components](../design-system/components/)
- [Design Tokens Reference](../design-system/tokens/)
- [Best Practices](./BEST_PRACTICES.md)
- [Troubleshooting](./TROUBLESHOOTING.md)

### Herramientas

- **Storybook**: Para comparar componentes antes/después
- **Chromatic**: Para visual regression testing
- **Bundle Analyzer**: Para verificar impacto en bundle size

### Soporte

- **Slack**: #design-system-migration
- **GitHub Issues**: Para reportar problemas
- **Office Hours**: Martes y jueves 15:00-16:00

## ✅ Checklist Final

### Componente Migrado

- [ ] Importa del design system unificado
- [ ] Usa design tokens en lugar de valores hardcoded
- [ ] Mantiene API compatibility o proporciona mapper
- [ ] Pasa todos los tests existentes
- [ ] Cumple estándares de accesibilidad
- [ ] Está documentado en Storybook
- [ ] Performance igual o mejor que legacy

### Proyecto Completo

- [ ] Todos los componentes migrados
- [ ] Código legacy eliminado
- [ ] Bundle size optimizado
- [ ] Tests actualizados
- [ ] Documentación actualizada
- [ ] Quality gates pasando

---

**¡Migración exitosa!** Tu proyecto ahora utiliza el Design System ATLAS v2.4 completo, proporcionando consistencia, performance y mantenibilidad mejoradas.
