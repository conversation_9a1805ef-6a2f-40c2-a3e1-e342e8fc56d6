/**
 * ATLAS v2.4 Design System Hook - useDesignTokens
 * Centralized access to design tokens with theme awareness
 */

import { useMemo } from 'react'
import { useTheme } from '@/context/ThemeContext'

// Import all design tokens
import { 
  baseColors, 
  lightTheme, 
  darkTheme, 
  gradients, 
  shadows,
  colorUtils,
  type ThemeColors 
} from '../tokens/colors'
import { 
  fontFamilies, 
  fontWeights, 
  fontSizes, 
  textStyles, 
  responsiveTextStyles,
  typographyUtils,
  type TextStyle 
} from '../tokens/typography'
import { 
  spacing, 
  componentSpacing, 
  borderRadius, 
  borderWidth, 
  zIndex, 
  breakpoints,
  containerSizes,
  spacingUtils,
  type SpacingKey 
} from '../tokens/spacing'
import { 
  animationVariants, 
  cssAnimations, 
  animationUtils,
  type AnimationVariant 
} from '../tokens/animations'

/**
 * Design tokens interface
 */
interface DesignTokens {
  // Colors
  colors: {
    base: typeof baseColors
    theme: ThemeColors
    gradients: typeof gradients
    shadows: typeof shadows
    utils: typeof colorUtils
  }
  
  // Typography
  typography: {
    families: typeof fontFamilies
    weights: typeof fontWeights
    sizes: typeof fontSizes
    styles: typeof textStyles
    responsive: typeof responsiveTextStyles
    utils: typeof typographyUtils
  }
  
  // Spacing
  spacing: {
    scale: typeof spacing
    components: typeof componentSpacing
    radius: typeof borderRadius
    borders: typeof borderWidth
    zIndex: typeof zIndex
    breakpoints: typeof breakpoints
    containers: typeof containerSizes
    utils: typeof spacingUtils
  }
  
  // Animations
  animations: {
    variants: typeof animationVariants
    css: typeof cssAnimations
    utils: typeof animationUtils
  }
  
  // Component-specific tokens
  button: {
    variants: ButtonVariants
    sizes: ButtonSizes
    effects: ButtonEffects
  }
}

/**
 * Button-specific design tokens
 */
interface ButtonVariants {
  primary: {
    light: string
    dark: string
  }
  secondary: {
    light: string
    dark: string
  }
  outline: {
    light: string
    dark: string
  }
  accent: {
    light: string
    dark: string
  }
  gradient: {
    light: string
    dark: string
  }
  ghost: {
    light: string
    dark: string
  }
}

interface ButtonSizes {
  sm: {
    padding: string
    fontSize: string
    height: string
  }
  md: {
    padding: string
    fontSize: string
    height: string
  }
  lg: {
    padding: string
    fontSize: string
    height: string
  }
  xl: {
    padding: string
    fontSize: string
    height: string
  }
}

interface ButtonEffects {
  none: string
  ripple: string
  particles: string
  glow: string
}

/**
 * Hook to access design tokens with theme awareness
 */
export const useDesignTokens = (): DesignTokens => {
  const { resolvedTheme } = useTheme()
  
  // Memoize tokens to prevent unnecessary re-renders
  const tokens = useMemo((): DesignTokens => {
    // Get theme-specific colors
    const themeColors = resolvedTheme === 'dark' ? darkTheme : lightTheme
    
    // Button variants with theme-aware colors
    const buttonVariants: ButtonVariants = {
      primary: {
        light: `bg-gradient-to-r from-[${baseColors.primary[500]}] to-[${baseColors.secondary[500]}] text-white hover:from-[${baseColors.primary[600]}] hover:to-[${baseColors.secondary[600]}] shadow-lg hover:shadow-[${baseColors.primary[500]}]/20`,
        dark: `bg-gradient-to-r from-[${baseColors.primary[400]}] to-[${baseColors.secondary[400]}] text-white hover:from-[${baseColors.primary[500]}] hover:to-[${baseColors.secondary[500]}] shadow-lg hover:shadow-[${baseColors.primary[400]}]/20`
      },
      secondary: {
        light: `bg-white text-[${baseColors.neutral[900]}] border border-[${baseColors.neutral[300]}] hover:bg-[${baseColors.neutral[50]}] shadow-sm hover:shadow-md`,
        dark: `bg-[${baseColors.neutral[800]}] text-[${baseColors.neutral[100]}] border border-[${baseColors.neutral[600]}] hover:bg-[${baseColors.neutral[700]}] shadow-sm hover:shadow-md`
      },
      outline: {
        light: `bg-transparent border-2 border-[${baseColors.primary[500]}]/50 text-[${baseColors.primary[500]}] hover:bg-[${baseColors.primary[500]}]/10 hover:border-[${baseColors.primary[500]}]`,
        dark: `bg-transparent border-2 border-[${baseColors.primary[400]}]/50 text-[${baseColors.primary[400]}] hover:bg-[${baseColors.primary[400]}]/10 hover:border-[${baseColors.primary[400]}]`
      },
      accent: {
        light: `bg-[${baseColors.accent[500]}] text-white hover:bg-[${baseColors.accent[600]}] shadow-lg hover:shadow-[${baseColors.accent[500]}]/20`,
        dark: `bg-[${baseColors.accent[400]}] text-white hover:bg-[${baseColors.accent[500]}] shadow-lg hover:shadow-[${baseColors.accent[400]}]/20`
      },
      gradient: {
        light: gradients.primary,
        dark: gradients.primary
      },
      ghost: {
        light: `bg-transparent text-[${baseColors.primary[500]}] hover:bg-[${baseColors.primary[500]}]/10`,
        dark: `bg-transparent text-[${baseColors.primary[400]}] hover:bg-[${baseColors.primary[400]}]/10`
      }
    }
    
    // Button sizes
    const buttonSizes: ButtonSizes = {
      sm: {
        padding: `${componentSpacing.button.padding.sm.y} ${componentSpacing.button.padding.sm.x}`,
        fontSize: fontSizes.sm.fontSize,
        height: spacing[8]
      },
      md: {
        padding: `${componentSpacing.button.padding.md.y} ${componentSpacing.button.padding.md.x}`,
        fontSize: fontSizes.base.fontSize,
        height: spacing[10]
      },
      lg: {
        padding: `${componentSpacing.button.padding.lg.y} ${componentSpacing.button.padding.lg.x}`,
        fontSize: fontSizes.lg.fontSize,
        height: spacing[12]
      },
      xl: {
        padding: `${componentSpacing.button.padding.xl.y} ${componentSpacing.button.padding.xl.x}`,
        fontSize: fontSizes.xl.fontSize,
        height: spacing[14]
      }
    }
    
    // Button effects
    const buttonEffects: ButtonEffects = {
      none: '',
      ripple: 'relative overflow-hidden',
      particles: 'relative overflow-hidden',
      glow: 'relative'
    }
    
    return {
      colors: {
        base: baseColors,
        theme: themeColors,
        gradients,
        shadows,
        utils: colorUtils
      },
      typography: {
        families: fontFamilies,
        weights: fontWeights,
        sizes: fontSizes,
        styles: textStyles,
        responsive: responsiveTextStyles,
        utils: typographyUtils
      },
      spacing: {
        scale: spacing,
        components: componentSpacing,
        radius: borderRadius,
        borders: borderWidth,
        zIndex,
        breakpoints,
        containers: containerSizes,
        utils: spacingUtils
      },
      animations: {
        variants: animationVariants,
        css: cssAnimations,
        utils: animationUtils
      },
      button: {
        variants: buttonVariants,
        sizes: buttonSizes,
        effects: buttonEffects
      }
    }
  }, [resolvedTheme])
  
  return tokens
}

/**
 * Hook to get theme-aware button styles
 */
export const useButtonStyles = (
  variant: keyof ButtonVariants = 'primary',
  size: keyof ButtonSizes = 'md',
  disabled: boolean = false,
  loading: boolean = false
) => {
  const tokens = useDesignTokens()
  const { resolvedTheme } = useTheme()
  
  return useMemo(() => {
    const variantStyles = tokens.button.variants[variant][resolvedTheme]
    const sizeStyles = tokens.button.sizes[size]
    
    const baseStyles = [
      'inline-flex items-center justify-center',
      'font-medium rounded-lg transition-all duration-300',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'select-none'
    ].join(' ')
    
    const stateStyles = []
    if (disabled) stateStyles.push('opacity-50 cursor-not-allowed')
    if (loading) stateStyles.push('cursor-wait')
    
    return {
      base: baseStyles,
      variant: variantStyles,
      size: `${sizeStyles.padding} ${sizeStyles.fontSize} h-[${sizeStyles.height}]`,
      state: stateStyles.join(' ')
    }
  }, [tokens, variant, size, disabled, loading, resolvedTheme])
}

/**
 * Hook to get theme-aware text styles
 */
export const useTextStyles = (style: TextStyle) => {
  const tokens = useDesignTokens()
  
  return useMemo(() => {
    return tokens.typography.utils.getTextStyleCSS(style)
  }, [tokens, style])
}

/**
 * Hook to get responsive spacing
 */
export const useResponsiveSpacing = (
  property: string,
  mobile: SpacingKey,
  tablet?: SpacingKey,
  desktop?: SpacingKey
) => {
  const tokens = useDesignTokens()
  
  return useMemo(() => {
    return tokens.spacing.utils.getResponsiveSpacing(
      property as any,
      mobile,
      tablet,
      desktop
    )
  }, [tokens, property, mobile, tablet, desktop])
}

export default useDesignTokens
