name: CI/CD Pipeline - ATLAS v2.4

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  # Quality Gates - Code Quality Check
  quality-gates:
    name: Quality Gates
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Type checking
      run: npm run type-check
      
    - name: Linting
      run: npm run lint
      
    - name: Code formatting check
      run: npm run format:check

  # Testing - Unit and Integration Tests
  testing:
    name: Testing Suite
    runs-on: ubuntu-latest
    needs: quality-gates
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests with coverage
      run: npm run test:ci
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  # Build and Performance Check
  build-and-performance:
    name: Build & Performance
    runs-on: ubuntu-latest
    needs: [quality-gates, testing]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      
    - name: Analyze bundle size
      run: npm run analyze
      
    - name: Check bundle size limits
      run: |
        # Check if bundle size exceeds 1.5MB (ATLAS limit)
        BUNDLE_SIZE=$(du -sb out | cut -f1)
        MAX_SIZE=1572864  # 1.5MB in bytes
        if [ $BUNDLE_SIZE -gt $MAX_SIZE ]; then
          echo "Bundle size ($BUNDLE_SIZE bytes) exceeds limit ($MAX_SIZE bytes)"
          exit 1
        fi
        echo "Bundle size check passed: $BUNDLE_SIZE bytes"

  # Storybook Build
  storybook:
    name: Storybook Build
    runs-on: ubuntu-latest
    needs: quality-gates
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build Storybook
      run: npm run build-storybook
      
    - name: Upload Storybook artifacts
      uses: actions/upload-artifact@v3
      with:
        name: storybook-build
        path: storybook-static/

  # Security Audit
  security:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run security audit
      run: npm audit --audit-level=moderate
      
    - name: Check for known vulnerabilities
      run: npx audit-ci --moderate

  # Deployment (only on main branch)
  deploy:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [quality-gates, testing, build-and-performance, storybook, security]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build for production
      run: npm run build
      
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your deployment commands here
        
    - name: Run smoke tests
      run: |
        echo "Running smoke tests..."
        # Add smoke test commands here
        
    - name: Deploy to production
      run: |
        echo "Deploying to production..."
        # Add production deployment commands here

  # ATLAS Metrics Collection
  atlas-metrics:
    name: ATLAS Metrics
    runs-on: ubuntu-latest
    needs: [build-and-performance]
    if: always()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Collect ATLAS metrics
      run: |
        echo "Collecting ATLAS v2.4 metrics..."
        
        # Count lines of code
        TOTAL_LINES=$(find src -name "*.tsx" -o -name "*.ts" | xargs wc -l | tail -1 | awk '{print $1}')
        echo "Total lines of code: $TOTAL_LINES"
        
        # Count components over 200 lines
        LARGE_COMPONENTS=$(find src -name "*.tsx" -o -name "*.ts" | xargs wc -l | awk '$1 > 200 {count++} END {print count+0}')
        echo "Components over 200 lines: $LARGE_COMPONENTS"
        
        # Check if metrics meet ATLAS standards
        if [ $LARGE_COMPONENTS -gt 0 ]; then
          echo "⚠️  ATLAS Quality Gate: $LARGE_COMPONENTS components exceed 200 lines"
        else
          echo "✅ ATLAS Quality Gate: All components under 200 lines"
        fi
