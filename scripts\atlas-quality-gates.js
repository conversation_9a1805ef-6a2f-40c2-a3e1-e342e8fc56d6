#!/usr/bin/env node

/**
 * ATLAS v2.4 - Quality Gates Validation
 * Comprehensive validation of all ATLAS v2.4 standards and requirements
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
  dim: '\x1b[2m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// ATLAS v2.4 Quality Gates Configuration
const QUALITY_GATES = [
  {
    name: 'Design System Validation',
    script: 'validate:design-system',
    description: 'Validates design system compliance and token usage',
    critical: true,
    weight: 20,
  },
  {
    name: 'Bundle Size Validation',
    script: 'validate:bundle-size',
    description: 'Validates bundle size limits and performance budgets',
    critical: true,
    weight: 15,
  },
  {
    name: 'Accessibility Validation',
    script: 'validate:accessibility',
    description: 'Validates WCAG 2.1 AA compliance',
    critical: true,
    weight: 20,
  },
  {
    name: 'Performance Budget',
    script: 'validate:performance',
    description: 'Validates Core Web Vitals and performance metrics',
    critical: true,
    weight: 15,
  },
  {
    name: 'Code Quality',
    script: 'quality',
    description: 'ESLint, TypeScript, and code quality checks',
    critical: true,
    weight: 10,
  },
  {
    name: 'Unit Tests',
    script: 'test:ci',
    description: 'Unit and integration test coverage',
    critical: true,
    weight: 15,
  },
  {
    name: 'E2E Tests',
    script: 'test:e2e',
    description: 'End-to-end critical user flow tests',
    critical: false,
    weight: 5,
  },
];

/**
 * Execute a quality gate
 */
async function executeQualityGate(gate) {
  log(`\n🔍 Running: ${gate.name}`, 'blue');
  log(`   ${gate.description}`, 'dim');
  
  const startTime = Date.now();
  
  try {
    execSync(`npm run ${gate.script}`, { 
      stdio: 'pipe',
      timeout: 300000 // 5 minutes timeout
    });
    
    const duration = Date.now() - startTime;
    log(`   ✅ PASSED (${duration}ms)`, 'green');
    
    return {
      name: gate.name,
      passed: true,
      duration: duration,
      weight: gate.weight,
      critical: gate.critical,
      error: null,
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    log(`   ❌ FAILED (${duration}ms)`, 'red');
    
    if (error.stdout) {
      log(`   Output: ${error.stdout.toString().slice(0, 200)}...`, 'dim');
    }
    
    return {
      name: gate.name,
      passed: false,
      duration: duration,
      weight: gate.weight,
      critical: gate.critical,
      error: error.message,
    };
  }
}

/**
 * Calculate quality score
 */
function calculateQualityScore(results) {
  const totalWeight = QUALITY_GATES.reduce((sum, gate) => sum + gate.weight, 0);
  const achievedWeight = results
    .filter(result => result.passed)
    .reduce((sum, result) => sum + result.weight, 0);
  
  return Math.round((achievedWeight / totalWeight) * 100);
}

/**
 * Generate quality report
 */
function generateQualityReport(results, score) {
  const report = {
    timestamp: new Date().toISOString(),
    atlas_version: '2.4',
    overall_score: score,
    passed: results.every(r => !r.critical || r.passed),
    critical_failures: results.filter(r => r.critical && !r.passed).length,
    total_duration: results.reduce((sum, r) => sum + r.duration, 0),
    results: results,
    summary: {
      total_gates: results.length,
      passed_gates: results.filter(r => r.passed).length,
      failed_gates: results.filter(r => !r.passed).length,
      critical_gates: results.filter(r => r.critical).length,
      critical_passed: results.filter(r => r.critical && r.passed).length,
    },
    recommendations: [],
  };
  
  // Add recommendations based on failures
  const failedGates = results.filter(r => !r.passed);
  failedGates.forEach(gate => {
    switch (gate.name) {
      case 'Design System Validation':
        report.recommendations.push('Review component implementations for design system compliance');
        break;
      case 'Bundle Size Validation':
        report.recommendations.push('Optimize bundle size with code splitting and tree shaking');
        break;
      case 'Accessibility Validation':
        report.recommendations.push('Fix accessibility issues for WCAG 2.1 AA compliance');
        break;
      case 'Performance Budget':
        report.recommendations.push('Optimize performance metrics and Core Web Vitals');
        break;
      case 'Code Quality':
        report.recommendations.push('Fix ESLint errors and TypeScript issues');
        break;
      case 'Unit Tests':
        report.recommendations.push('Improve test coverage and fix failing tests');
        break;
      case 'E2E Tests':
        report.recommendations.push('Fix end-to-end test failures');
        break;
    }
  });
  
  // Save report
  const reportPath = 'reports/atlas-quality-gates-report.json';
  fs.mkdirSync(path.dirname(reportPath), { recursive: true });
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  return report;
}

/**
 * Display quality gate results
 */
function displayResults(results, score) {
  log('\n' + '='.repeat(60), 'cyan');
  log('🏆 ATLAS v2.4 QUALITY GATES RESULTS', 'cyan');
  log('='.repeat(60), 'cyan');
  
  // Overall score
  const scoreColor = score >= 90 ? 'green' : score >= 70 ? 'yellow' : 'red';
  log(`\n📊 Overall Quality Score: ${score}%`, scoreColor);
  
  // Results table
  log('\n📋 Quality Gates Summary:', 'bold');
  log('┌─────────────────────────────────┬──────────┬──────────┬──────────┐', 'dim');
  log('│ Quality Gate                    │ Status   │ Critical │ Weight   │', 'dim');
  log('├─────────────────────────────────┼──────────┼──────────┼──────────┤', 'dim');
  
  results.forEach(result => {
    const name = result.name.padEnd(31);
    const status = result.passed ? '✅ PASS  ' : '❌ FAIL  ';
    const critical = result.critical ? '🔴 YES  ' : '⚪ NO   ';
    const weight = `${result.weight}%`.padStart(7);
    
    const statusColor = result.passed ? 'green' : 'red';
    log(`│ ${name} │ ${status} │ ${critical} │ ${weight} │`, statusColor);
  });
  
  log('└─────────────────────────────────┴──────────┴──────────┴──────────┘', 'dim');
  
  // Critical failures
  const criticalFailures = results.filter(r => r.critical && !r.passed);
  if (criticalFailures.length > 0) {
    log(`\n🚨 Critical Failures (${criticalFailures.length}):`, 'red');
    criticalFailures.forEach(failure => {
      log(`   • ${failure.name}`, 'red');
    });
  }
  
  // Performance summary
  const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
  log(`\n⏱️  Total Execution Time: ${(totalDuration / 1000).toFixed(2)}s`, 'blue');
  
  // Recommendations
  const failedGates = results.filter(r => !r.passed);
  if (failedGates.length > 0) {
    log('\n💡 Next Steps:', 'yellow');
    failedGates.forEach(gate => {
      log(`   • Fix ${gate.name} issues`, 'yellow');
    });
  }
}

/**
 * Main quality gates execution
 */
async function runQualityGates() {
  log('🚀 ATLAS v2.4 Quality Gates Validation Starting...', 'magenta');
  log('====================================================', 'magenta');
  
  const startTime = Date.now();
  const results = [];
  
  // Execute each quality gate
  for (const gate of QUALITY_GATES) {
    const result = await executeQualityGate(gate);
    results.push(result);
    
    // Stop on critical failure if in CI
    if (result.critical && !result.passed && process.env.CI) {
      log('\n🛑 Critical quality gate failed in CI environment', 'red');
      break;
    }
  }
  
  // Calculate score and generate report
  const score = calculateQualityScore(results);
  const report = generateQualityReport(results, score);
  
  // Display results
  displayResults(results, score);
  
  // Final verdict
  const totalTime = Date.now() - startTime;
  log(`\n⏱️  Total Quality Gates Time: ${(totalTime / 1000).toFixed(2)}s`, 'blue');
  
  if (report.passed) {
    log('\n🎉 ALL ATLAS v2.4 QUALITY GATES PASSED!', 'green');
    log('✅ Application meets all ATLAS v2.4 standards', 'green');
    log('🚀 Ready for production deployment', 'green');
  } else {
    log('\n❌ Some quality gates failed', 'red');
    log(`🔧 ${report.critical_failures} critical issues need attention`, 'red');
    
    if (report.recommendations.length > 0) {
      log('\n📝 Recommendations:', 'yellow');
      report.recommendations.forEach(rec => {
        log(`   • ${rec}`, 'yellow');
      });
    }
  }
  
  log(`\n📄 Detailed report: reports/atlas-quality-gates-report.json`, 'blue');
  
  return report.passed;
}

// Execute quality gates
if (require.main === module) {
  runQualityGates()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      log(`💥 Quality gates execution failed: ${error.message}`, 'red');
      process.exit(1);
    });
}

module.exports = { runQualityGates, QUALITY_GATES };
