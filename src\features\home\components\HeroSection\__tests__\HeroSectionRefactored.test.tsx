import { render, screen, waitFor } from '@testing-library/react'

import HeroSectionRefactored from '../HeroSectionRefactored'
import { ThemeProvider } from '@/context/ThemeContext'

// Mock the typing animation hook
jest.mock('../../hooks/useTypingAnimation', () => ({
  useTypingAnimation: jest.fn(() => ({
    currentText: 'Formación',
    isTyping: true,
    isPaused: false,
    currentPhrase: 'Formación In Company',
    progress: 0.5,
    pause: jest.fn(),
    resume: jest.fn(),
    reset: jest.fn(),
  })),
}))

// Mock media query hook
jest.mock('@/hooks/useMediaQuery', () => ({
  useMediaQuery: jest.fn(() => false), // Default to desktop
}))

// Test wrapper
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider>{children}</ThemeProvider>
)

describe('HeroSectionRefactored', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Rendering', () => {
    it('renders without crashing', async () => {
      render(
        <TestWrapper>
          <HeroSectionRefactored />
        </TestWrapper>
      )

      // Wait for component to mount
      await waitFor(() => {
        expect(screen.getByText(/Transformamos/i)).toBeInTheDocument()
      })
    })

    it('displays the main title', async () => {
      render(
        <TestWrapper>
          <HeroSectionRefactored />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText(/Transformamos/i)).toBeInTheDocument()
        expect(screen.getByText(/Ideas/i)).toBeInTheDocument()
        expect(screen.getByText(/Realidad/i)).toBeInTheDocument()
        expect(screen.getByText(/con IA/i)).toBeInTheDocument()
      })
    })

    it('displays the subtitle description', async () => {
      render(
        <TestWrapper>
          <HeroSectionRefactored />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(
          screen.getByText(/Especialistas en soluciones de/i)
        ).toBeInTheDocument()
        expect(screen.getByText(/Inteligencia Artificial/i)).toBeInTheDocument()
      })
    })

    it('displays typing animation text', async () => {
      render(
        <TestWrapper>
          <HeroSectionRefactored />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Formación')).toBeInTheDocument()
      })
    })

    it('displays CTA buttons', async () => {
      render(
        <TestWrapper>
          <HeroSectionRefactored />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(
          screen.getByRole('link', { name: /explorar nuestros servicios/i })
        ).toBeInTheDocument()
        expect(
          screen.getByRole('link', { name: /consulta gratuita/i })
        ).toBeInTheDocument()
      })
    })
  })

  describe('Responsive behavior', () => {
    it('uses mobile phrases on mobile devices', async () => {
      const { useMediaQuery } = require('@/hooks/useMediaQuery')
      const { useTypingAnimation } = require('../../hooks/useTypingAnimation')

      // Mock mobile detection
      useMediaQuery.mockReturnValue(true)

      // Mock typing animation with mobile phrases
      useTypingAnimation.mockReturnValue({
        currentText: 'Formación',
        isTyping: true,
        isPaused: false,
        currentPhrase: 'Formación',
        progress: 1,
        pause: jest.fn(),
        resume: jest.fn(),
        reset: jest.fn(),
      })

      render(
        <TestWrapper>
          <HeroSectionRefactored />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(useTypingAnimation).toHaveBeenCalledWith({
          phrases: ['Formación', 'Asesoría', 'Cursos', 'Automatización', 'Desarrollo'],
          typingSpeed: 150,
          deletingSpeed: 80,
          pauseDuration: 1500,
          loop: true,
        })
      })
    })

    it('uses desktop phrases on desktop devices', async () => {
      const { useMediaQuery } = require('@/hooks/useMediaQuery')
      const { useTypingAnimation } = require('../../hooks/useTypingAnimation')

      // Mock desktop detection
      useMediaQuery.mockReturnValue(false)

      render(
        <TestWrapper>
          <HeroSectionRefactored />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(useTypingAnimation).toHaveBeenCalledWith({
          phrases: [
            'Formación In Company',
            'Asesoría Estratégica',
            'Desarrollo de Cursos',
            'Automatizaciones',
            'Desarrollo a Medida',
          ],
          typingSpeed: 150,
          deletingSpeed: 80,
          pauseDuration: 1500,
          loop: true,
        })
      })
    })
  })

  describe('Loading state', () => {
    it('shows loading spinner before mount', () => {
      // Mock useState to return false for isMounted
      const mockUseState = jest.spyOn(React, 'useState')
      mockUseState.mockImplementationOnce(() => [false, jest.fn()])

      render(
        <TestWrapper>
          <HeroSectionRefactored />
        </TestWrapper>
      )

      // Should show loading spinner
      const spinner = document.querySelector('.animate-spin')
      expect(spinner).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('has proper semantic structure', async () => {
      render(
        <TestWrapper>
          <HeroSectionRefactored />
        </TestWrapper>
      )

      await waitFor(() => {
        // Should have main heading
        const mainHeading = screen.getByRole('heading', { level: 1 })
        expect(mainHeading).toBeInTheDocument()

        // Should have secondary heading for typing text
        const secondaryHeading = screen.getByRole('heading', { level: 2 })
        expect(secondaryHeading).toBeInTheDocument()

        // Should have navigation links
        const links = screen.getAllByRole('link')
        expect(links).toHaveLength(2)
      })
    })

    it('has proper ARIA attributes', async () => {
      render(
        <TestWrapper>
          <HeroSectionRefactored />
        </TestWrapper>
      )

      await waitFor(() => {
        // Cursor should be hidden from screen readers
        const cursor = document.querySelector('[aria-hidden="true"]')
        expect(cursor).toBeInTheDocument()
      })
    })
  })

  describe('Performance', () => {
    it('memoizes expensive operations', async () => {
      const { useTypingAnimation } = require('../../hooks/useTypingAnimation')

      // Render component twice
      const { rerender } = render(
        <TestWrapper>
          <HeroSectionRefactored />
        </TestWrapper>
      )

      rerender(
        <TestWrapper>
          <HeroSectionRefactored />
        </TestWrapper>
      )

      await waitFor(() => {
        // useTypingAnimation should be called for each render
        expect(useTypingAnimation).toHaveBeenCalledTimes(2)
      })
    })
  })
})
