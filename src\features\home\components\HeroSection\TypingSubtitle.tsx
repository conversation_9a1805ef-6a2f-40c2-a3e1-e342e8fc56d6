'use client'

import React from 'react'
import { motion } from 'framer-motion'

interface TypingSubtitleProps {
  text: string
  isTyping?: boolean
  className?: string
}

/**
 * Typing subtitle component for HeroSection
 * Displays animated typing text with cursor effect
 */
const TypingSubtitle: React.FC<TypingSubtitleProps> = ({
  text,
  isTyping = false,
  className = '',
}) => {
  return (
    <div className={`relative ${className}`}>
      <motion.h2
        className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#00F0FF] to-[#48D1CC] min-h-[1.2em] flex items-center justify-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <span className="inline-block">
          {text}
          <motion.span
            className="inline-block w-0.5 h-[0.9em] bg-[#00F0FF] ml-1"
            animate={{
              opacity: isTyping ? [1, 1, 0, 0] : [1, 0],
            }}
            transition={{
              duration: isTyping ? 1 : 0.8,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
            aria-hidden="true"
          />
        </span>
      </motion.h2>
    </div>
  )
}

export default TypingSubtitle
