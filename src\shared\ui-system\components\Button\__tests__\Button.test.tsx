import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ThemeProvider } from '@/context/ThemeContext'
import But<PERSON> from '../Button'

// Test wrapper with theme context
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider>{children}</ThemeProvider>
)

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    span: ({ children, ...props }: any) => <span {...props}>{children}</span>,
  },
  AnimatePresence: ({ children }: any) => children,
}))

describe('Button Component - ATLAS v2.4 Unified', () => {
  describe('Basic Rendering', () => {
    it('renders with default props', () => {
      render(
        <TestWrapper>
          <Button>Click me</Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button', { name: /click me/i })
      expect(button).toBeInTheDocument()
      expect(button).toHaveClass('inline-flex', 'items-center', 'justify-center')
    })

    it('renders with custom className', () => {
      render(
        <TestWrapper>
          <Button className="custom-class">Button</Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      expect(button).toHaveClass('custom-class')
    })

    it('renders as full width when specified', () => {
      render(
        <TestWrapper>
          <Button fullWidth>Full Width</Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      expect(button).toHaveClass('w-full')
    })
  })

  describe('Variants', () => {
    const variants = ['primary', 'secondary', 'outline', 'accent', 'gradient', 'ghost'] as const

    variants.forEach(variant => {
      it(`renders ${variant} variant correctly`, () => {
        render(
          <TestWrapper>
            <Button variant={variant}>{variant} Button</Button>
          </TestWrapper>
        )
        
        const button = screen.getByRole('button')
        expect(button).toBeInTheDocument()
        // Variant-specific classes are applied through design tokens
      })
    })
  })

  describe('Sizes', () => {
    const sizes = ['sm', 'md', 'lg', 'xl'] as const

    sizes.forEach(size => {
      it(`renders ${size} size correctly`, () => {
        render(
          <TestWrapper>
            <Button size={size}>{size} Button</Button>
          </TestWrapper>
        )
        
        const button = screen.getByRole('button')
        expect(button).toBeInTheDocument()
        // Size-specific classes are applied through design tokens
      })
    })
  })

  describe('States', () => {
    it('renders disabled state correctly', () => {
      render(
        <TestWrapper>
          <Button disabled>Disabled Button</Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      expect(button).toBeDisabled()
      expect(button).toHaveAttribute('aria-disabled', 'true')
    })

    it('renders loading state correctly', () => {
      render(
        <TestWrapper>
          <Button loading>Loading Button</Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      expect(button).toBeDisabled()
      expect(button).toHaveAttribute('aria-disabled', 'true')
      
      // Check for loading spinner
      const spinner = button.querySelector('.border-t-transparent')
      expect(spinner).toBeInTheDocument()
    })

    it('does not trigger click when disabled', async () => {
      const handleClick = jest.fn()
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Button disabled onClick={handleClick}>
            Disabled Button
          </Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      await user.click(button)
      
      expect(handleClick).not.toHaveBeenCalled()
    })

    it('does not trigger click when loading', async () => {
      const handleClick = jest.fn()
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Button loading onClick={handleClick}>
            Loading Button
          </Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      await user.click(button)
      
      expect(handleClick).not.toHaveBeenCalled()
    })
  })

  describe('Icons', () => {
    const TestIcon = () => <span data-testid="test-icon">🚀</span>

    it('renders icon on the right by default', () => {
      render(
        <TestWrapper>
          <Button icon={<TestIcon />}>With Icon</Button>
        </TestWrapper>
      )
      
      const icon = screen.getByTestId('test-icon')
      const button = screen.getByRole('button')
      
      expect(icon).toBeInTheDocument()
      expect(button).toContainElement(icon)
    })

    it('renders icon on the left when specified', () => {
      render(
        <TestWrapper>
          <Button icon={<TestIcon />} iconPosition="left">
            With Icon Left
          </Button>
        </TestWrapper>
      )
      
      const icon = screen.getByTestId('test-icon')
      expect(icon).toBeInTheDocument()
    })

    it('hides icon when loading', () => {
      render(
        <TestWrapper>
          <Button icon={<TestIcon />} loading>
            Loading with Icon
          </Button>
        </TestWrapper>
      )
      
      const icon = screen.queryByTestId('test-icon')
      expect(icon).not.toBeInTheDocument()
    })
  })

  describe('Link Behavior', () => {
    it('renders as link when href is provided', () => {
      render(
        <TestWrapper>
          <Button href="/test">Link Button</Button>
        </TestWrapper>
      )
      
      const link = screen.getByRole('link', { name: /link button/i })
      expect(link).toBeInTheDocument()
      expect(link).toHaveAttribute('href', '/test')
    })

    it('renders external link correctly', () => {
      render(
        <TestWrapper>
          <Button href="https://example.com" external>
            External Link
          </Button>
        </TestWrapper>
      )
      
      const link = screen.getByRole('link')
      expect(link).toHaveAttribute('href', 'https://example.com')
      expect(link).toHaveAttribute('target', '_blank')
      expect(link).toHaveAttribute('rel', 'noopener noreferrer')
    })

    it('renders with custom target', () => {
      render(
        <TestWrapper>
          <Button href="/test" target="_self">
            Custom Target
          </Button>
        </TestWrapper>
      )
      
      const link = screen.getByRole('link')
      expect(link).toHaveAttribute('target', '_self')
    })
  })

  describe('Effects', () => {
    it('applies ripple effect classes when specified', () => {
      render(
        <TestWrapper>
          <Button effect="ripple">Ripple Button</Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      expect(button).toHaveClass('relative', 'overflow-hidden')
    })

    it('applies particle effect classes when specified', () => {
      render(
        <TestWrapper>
          <Button effect="particles">Particle Button</Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      expect(button).toHaveClass('relative', 'overflow-hidden')
    })

    it('creates ripple effect on click', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Button effect="ripple">Ripple Button</Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      await user.click(button)
      
      // Ripple effect creates spans dynamically
      await waitFor(() => {
        const ripples = button.querySelectorAll('span[style*="position: absolute"]')
        expect(ripples.length).toBeGreaterThan(0)
      }, { timeout: 100 })
    })
  })

  describe('Interactions', () => {
    it('handles click events', async () => {
      const handleClick = jest.fn()
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Button onClick={handleClick}>Click me</Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      await user.click(button)
      
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it('handles mouse enter and leave events', async () => {
      const handleMouseEnter = jest.fn()
      const handleMouseLeave = jest.fn()
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Button onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
            Hover me
          </Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      
      await user.hover(button)
      expect(handleMouseEnter).toHaveBeenCalledTimes(1)
      
      await user.unhover(button)
      expect(handleMouseLeave).toHaveBeenCalledTimes(1)
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(
        <TestWrapper>
          <Button aria-label="Custom label" tooltip="Helpful tooltip">
            Button
          </Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('aria-label', 'Custom label')
      expect(button).toHaveAttribute('title', 'Helpful tooltip')
    })

    it('sets aria-disabled when disabled', () => {
      render(
        <TestWrapper>
          <Button disabled>Disabled</Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('aria-disabled', 'true')
    })

    it('sets aria-disabled when loading', () => {
      render(
        <TestWrapper>
          <Button loading>Loading</Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      expect(button).toHaveAttribute('aria-disabled', 'true')
    })

    it('supports keyboard navigation', async () => {
      const handleClick = jest.fn()
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <Button onClick={handleClick}>Keyboard Button</Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      button.focus()
      
      await user.keyboard('{Enter}')
      expect(handleClick).toHaveBeenCalledTimes(1)
      
      await user.keyboard(' ')
      expect(handleClick).toHaveBeenCalledTimes(2)
    })
  })

  describe('Theme Integration', () => {
    it('adapts to theme changes', () => {
      render(
        <TestWrapper>
          <Button>Themed Button</Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      expect(button).toBeInTheDocument()
      // Theme-specific styling is handled by design tokens
    })
  })

  describe('Performance', () => {
    it('memoizes expensive operations', () => {
      const { rerender } = render(
        <TestWrapper>
          <Button variant="primary">Button</Button>
        </TestWrapper>
      )
      
      // Re-render with same props should not cause issues
      rerender(
        <TestWrapper>
          <Button variant="primary">Button</Button>
        </TestWrapper>
      )
      
      const button = screen.getByRole('button')
      expect(button).toBeInTheDocument()
    })
  })
})
