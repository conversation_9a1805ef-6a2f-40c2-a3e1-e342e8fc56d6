/**
 * ATLAS v2.4 Design Tokens - Animations
 * Centralized animation system for consistent motion design
 */

import { durations, easings } from './spacing'

// Animation variants for Framer Motion
export const animationVariants = {
  // Button animations
  button: {
    initial: { scale: 1 },
    hover: { 
      scale: 1.02,
      transition: { duration: 0.15, ease: 'easeOut' }
    },
    tap: { 
      scale: 0.98,
      transition: { duration: 0.1, ease: 'easeInOut' }
    },
  },
  
  // Card animations
  card: {
    initial: { 
      opacity: 0, 
      y: 20,
      scale: 0.95
    },
    animate: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: { 
        duration: 0.3, 
        ease: 'easeOut',
        staggerChildren: 0.1
      }
    },
    hover: {
      y: -4,
      scale: 1.02,
      transition: { duration: 0.2, ease: 'easeOut' }
    },
    exit: {
      opacity: 0,
      y: -20,
      scale: 0.95,
      transition: { duration: 0.2, ease: 'easeIn' }
    }
  },
  
  // Modal animations
  modal: {
    overlay: {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 },
    },
    content: {
      initial: { 
        opacity: 0, 
        scale: 0.9,
        y: 20
      },
      animate: { 
        opacity: 1, 
        scale: 1,
        y: 0,
        transition: { 
          duration: 0.3, 
          ease: 'easeOut' 
        }
      },
      exit: { 
        opacity: 0, 
        scale: 0.9,
        y: 20,
        transition: { 
          duration: 0.2, 
          ease: 'easeIn' 
        }
      }
    }
  },
  
  // Page transitions
  page: {
    initial: { opacity: 0, x: 20 },
    animate: { 
      opacity: 1, 
      x: 0,
      transition: { 
        duration: 0.4, 
        ease: 'easeOut',
        staggerChildren: 0.1
      }
    },
    exit: { 
      opacity: 0, 
      x: -20,
      transition: { 
        duration: 0.3, 
        ease: 'easeIn' 
      }
    }
  },
  
  // Fade animations
  fade: {
    initial: { opacity: 0 },
    animate: { 
      opacity: 1,
      transition: { duration: 0.3, ease: 'easeOut' }
    },
    exit: { 
      opacity: 0,
      transition: { duration: 0.2, ease: 'easeIn' }
    }
  },
  
  // Slide animations
  slideUp: {
    initial: { opacity: 0, y: 30 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.4, ease: 'easeOut' }
    },
    exit: { 
      opacity: 0, 
      y: -30,
      transition: { duration: 0.3, ease: 'easeIn' }
    }
  },
  
  slideDown: {
    initial: { opacity: 0, y: -30 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.4, ease: 'easeOut' }
    },
    exit: { 
      opacity: 0, 
      y: 30,
      transition: { duration: 0.3, ease: 'easeIn' }
    }
  },
  
  slideLeft: {
    initial: { opacity: 0, x: 30 },
    animate: { 
      opacity: 1, 
      x: 0,
      transition: { duration: 0.4, ease: 'easeOut' }
    },
    exit: { 
      opacity: 0, 
      x: -30,
      transition: { duration: 0.3, ease: 'easeIn' }
    }
  },
  
  slideRight: {
    initial: { opacity: 0, x: -30 },
    animate: { 
      opacity: 1, 
      x: 0,
      transition: { duration: 0.4, ease: 'easeOut' }
    },
    exit: { 
      opacity: 0, 
      x: 30,
      transition: { duration: 0.3, ease: 'easeIn' }
    }
  },
  
  // Scale animations
  scale: {
    initial: { opacity: 0, scale: 0.8 },
    animate: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.3, ease: 'easeOut' }
    },
    exit: { 
      opacity: 0, 
      scale: 0.8,
      transition: { duration: 0.2, ease: 'easeIn' }
    }
  },
  
  // Stagger animations
  stagger: {
    animate: {
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  },
  
  // Loading animations
  loading: {
    rotate: {
      animate: {
        rotate: 360,
        transition: {
          duration: 1,
          repeat: Infinity,
          ease: 'linear'
        }
      }
    },
    pulse: {
      animate: {
        scale: [1, 1.1, 1],
        opacity: [0.7, 1, 0.7],
        transition: {
          duration: 1.5,
          repeat: Infinity,
          ease: 'easeInOut'
        }
      }
    },
    bounce: {
      animate: {
        y: [0, -10, 0],
        transition: {
          duration: 0.6,
          repeat: Infinity,
          ease: 'easeInOut'
        }
      }
    }
  },
  
  // Typing animation
  typing: {
    cursor: {
      animate: {
        opacity: [1, 0],
        transition: {
          duration: 0.8,
          repeat: Infinity,
          ease: 'easeInOut'
        }
      }
    }
  },
  
  // Particle effects
  particle: {
    initial: {
      opacity: 1,
      scale: 0,
      x: 0,
      y: 0
    },
    animate: {
      opacity: 0,
      scale: 1,
      x: (Math.random() - 0.5) * 100,
      y: (Math.random() - 0.5) * 100,
      transition: {
        duration: 0.8,
        ease: 'easeOut'
      }
    }
  },
  
  // Ripple effect
  ripple: {
    initial: {
      opacity: 0.6,
      scale: 0
    },
    animate: {
      opacity: 0,
      scale: 4,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  }
} as const

// CSS animations (for non-Framer Motion usage)
export const cssAnimations = {
  // Keyframes
  keyframes: {
    fadeIn: {
      '0%': { opacity: '0' },
      '100%': { opacity: '1' }
    },
    fadeOut: {
      '0%': { opacity: '1' },
      '100%': { opacity: '0' }
    },
    slideUp: {
      '0%': { transform: 'translateY(20px)', opacity: '0' },
      '100%': { transform: 'translateY(0)', opacity: '1' }
    },
    slideDown: {
      '0%': { transform: 'translateY(-20px)', opacity: '0' },
      '100%': { transform: 'translateY(0)', opacity: '1' }
    },
    scaleIn: {
      '0%': { transform: 'scale(0.8)', opacity: '0' },
      '100%': { transform: 'scale(1)', opacity: '1' }
    },
    spin: {
      '0%': { transform: 'rotate(0deg)' },
      '100%': { transform: 'rotate(360deg)' }
    },
    pulse: {
      '0%, 100%': { opacity: '0.7' },
      '50%': { opacity: '1' }
    },
    bounce: {
      '0%, 100%': { transform: 'translateY(0)' },
      '50%': { transform: 'translateY(-10px)' }
    }
  },
  
  // Animation classes
  classes: {
    fadeIn: {
      animation: `fadeIn ${durations.normal} ${easings.easeOut} forwards`
    },
    fadeOut: {
      animation: `fadeOut ${durations.normal} ${easings.easeIn} forwards`
    },
    slideUp: {
      animation: `slideUp ${durations.normal} ${easings.easeOut} forwards`
    },
    slideDown: {
      animation: `slideDown ${durations.normal} ${easings.easeOut} forwards`
    },
    scaleIn: {
      animation: `scaleIn ${durations.normal} ${easings.easeOut} forwards`
    },
    spin: {
      animation: `spin ${durations.slowest} linear infinite`
    },
    pulse: {
      animation: `pulse 1.5s ${easings.easeInOut} infinite`
    },
    bounce: {
      animation: `bounce 0.6s ${easings.easeInOut} infinite`
    }
  }
} as const

// Animation utilities
export const animationUtils = {
  /**
   * Get animation variant by name
   */
  getVariant: (name: keyof typeof animationVariants): any => {
    return animationVariants[name]
  },
  
  /**
   * Create custom transition
   */
  createTransition: (
    duration: keyof typeof durations,
    easing: keyof typeof easings,
    delay?: number
  ) => ({
    duration: parseFloat(durations[duration]) / 1000, // Convert to seconds
    ease: easings[easing],
    delay: delay || 0
  }),
  
  /**
   * Create stagger animation
   */
  createStagger: (
    staggerDelay: number = 0.1,
    delayChildren: number = 0
  ) => ({
    transition: {
      staggerChildren: staggerDelay,
      delayChildren
    }
  }),
  
  /**
   * Get CSS animation string
   */
  getCSSAnimation: (
    name: keyof typeof cssAnimations.keyframes,
    duration: keyof typeof durations = 'normal',
    easing: keyof typeof easings = 'easeOut',
    fillMode: 'forwards' | 'backwards' | 'both' | 'none' = 'forwards'
  ): string => {
    return `${name} ${durations[duration]} ${easings[easing]} ${fillMode}`
  }
} as const

export type AnimationVariant = keyof typeof animationVariants
export type CSSAnimationKeyframe = keyof typeof cssAnimations.keyframes
