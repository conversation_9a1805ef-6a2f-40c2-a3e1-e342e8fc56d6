#!/usr/bin/env node

/**
 * ATLAS v2.4 - Lighthouse Optimization Script
 * Automated Lighthouse auditing and optimization recommendations
 */

const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Lighthouse configuration
const LIGHTHOUSE_CONFIG = {
  extends: 'lighthouse:default',
  settings: {
    onlyAudits: [
      'first-contentful-paint',
      'largest-contentful-paint',
      'first-meaningful-paint',
      'speed-index',
      'interactive',
      'total-blocking-time',
      'cumulative-layout-shift',
      'server-response-time',
      'render-blocking-resources',
      'unused-css-rules',
      'unused-javascript',
      'modern-image-formats',
      'uses-optimized-images',
      'uses-text-compression',
      'uses-responsive-images',
      'efficient-animated-content',
      'preload-lcp-image',
      'color-contrast',
      'heading-order',
      'meta-description',
      'document-title',
      'html-has-lang',
      'image-alt',
      'link-name',
      'button-name',
      'aria-labels',
      'valid-lang',
      'meta-viewport',
      'installable-manifest',
      'splash-screen',
      'themed-omnibox',
      'content-width',
      'apple-touch-icon',
      'maskable-icon'
    ],
    emulatedFormFactor: 'mobile',
    throttling: {
      rttMs: 40,
      throughputKbps: 10240,
      cpuSlowdownMultiplier: 1,
      requestLatencyMs: 0,
      downloadThroughputKbps: 0,
      uploadThroughputKbps: 0
    },
    screenEmulation: {
      mobile: true,
      width: 375,
      height: 667,
      deviceScaleFactor: 2,
      disabled: false,
    }
  }
};

// Target scores (ATLAS v2.4 standards)
const TARGET_SCORES = {
  performance: 95,
  accessibility: 100,
  'best-practices': 100,
  seo: 100,
  pwa: 90
};

// URLs to audit
const URLS_TO_AUDIT = [
  { url: 'http://localhost:3000', name: 'Home' },
  { url: 'http://localhost:3000/about', name: 'About' },
  { url: 'http://localhost:3000/contact', name: 'Contact' },
  { url: 'http://localhost:3000/services', name: 'Services' }
];

/**
 * Launch Chrome and run Lighthouse audit
 */
async function runLighthouseAudit(url, name) {
  log(`\n🔍 Auditing: ${name} (${url})`, 'blue');
  
  const chrome = await chromeLauncher.launch({
    chromeFlags: [
      '--headless',
      '--disable-gpu',
      '--no-sandbox',
      '--disable-dev-shm-usage',
      '--disable-extensions'
    ]
  });
  
  try {
    const options = {
      logLevel: 'info',
      output: 'json',
      onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo', 'pwa'],
      port: chrome.port,
    };
    
    const runnerResult = await lighthouse(url, options, LIGHTHOUSE_CONFIG);
    
    await chrome.kill();
    
    return {
      name,
      url,
      scores: runnerResult.lhr.categories,
      audits: runnerResult.lhr.audits,
      report: runnerResult.report
    };
  } catch (error) {
    await chrome.kill();
    throw error;
  }
}

/**
 * Analyze audit results and generate recommendations
 */
function analyzeResults(results) {
  const analysis = {
    overall: true,
    pages: [],
    recommendations: [],
    criticalIssues: [],
    summary: {
      totalPages: results.length,
      passingPages: 0,
      averageScores: {}
    }
  };
  
  // Calculate average scores
  const scoreCategories = ['performance', 'accessibility', 'best-practices', 'seo', 'pwa'];
  scoreCategories.forEach(category => {
    const scores = results.map(r => r.scores[category]?.score * 100 || 0);
    analysis.summary.averageScores[category] = Math.round(
      scores.reduce((sum, score) => sum + score, 0) / scores.length
    );
  });
  
  // Analyze each page
  results.forEach(result => {
    const pageAnalysis = {
      name: result.name,
      url: result.url,
      scores: {},
      passed: true,
      issues: [],
      opportunities: []
    };
    
    // Check scores against targets
    Object.keys(TARGET_SCORES).forEach(category => {
      const score = Math.round((result.scores[category]?.score || 0) * 100);
      const target = TARGET_SCORES[category];
      
      pageAnalysis.scores[category] = {
        score,
        target,
        passed: score >= target
      };
      
      if (score < target) {
        pageAnalysis.passed = false;
        analysis.overall = false;
        
        pageAnalysis.issues.push({
          category,
          score,
          target,
          gap: target - score
        });
      }
    });
    
    // Extract opportunities from audits
    Object.values(result.audits).forEach(audit => {
      if (audit.score !== null && audit.score < 1 && audit.details?.overallSavingsMs > 100) {
        pageAnalysis.opportunities.push({
          id: audit.id,
          title: audit.title,
          description: audit.description,
          savings: audit.details.overallSavingsMs,
          impact: audit.details.overallSavingsMs > 1000 ? 'high' : 'medium'
        });
      }
    });
    
    if (pageAnalysis.passed) {
      analysis.summary.passingPages++;
    }
    
    analysis.pages.push(pageAnalysis);
  });
  
  // Generate global recommendations
  generateRecommendations(analysis);
  
  return analysis;
}

/**
 * Generate optimization recommendations
 */
function generateRecommendations(analysis) {
  const commonIssues = {};
  
  // Find common issues across pages
  analysis.pages.forEach(page => {
    page.opportunities.forEach(opp => {
      if (!commonIssues[opp.id]) {
        commonIssues[opp.id] = {
          ...opp,
          pages: []
        };
      }
      commonIssues[opp.id].pages.push(page.name);
    });
  });
  
  // Sort by impact and frequency
  const sortedIssues = Object.values(commonIssues)
    .sort((a, b) => (b.savings * b.pages.length) - (a.savings * a.pages.length));
  
  // Generate recommendations
  sortedIssues.forEach(issue => {
    let recommendation = '';
    
    switch (issue.id) {
      case 'unused-css-rules':
        recommendation = 'Remove unused CSS rules to reduce bundle size';
        break;
      case 'unused-javascript':
        recommendation = 'Remove unused JavaScript to improve loading performance';
        break;
      case 'render-blocking-resources':
        recommendation = 'Eliminate render-blocking resources by inlining critical CSS';
        break;
      case 'modern-image-formats':
        recommendation = 'Use modern image formats (WebP, AVIF) for better compression';
        break;
      case 'uses-optimized-images':
        recommendation = 'Optimize images by compressing and resizing appropriately';
        break;
      case 'preload-lcp-image':
        recommendation = 'Preload the Largest Contentful Paint image';
        break;
      case 'uses-text-compression':
        recommendation = 'Enable text compression (gzip/brotli) on server';
        break;
      default:
        recommendation = issue.description;
    }
    
    analysis.recommendations.push({
      id: issue.id,
      title: issue.title,
      recommendation,
      impact: issue.impact,
      savings: issue.savings,
      affectedPages: issue.pages,
      priority: issue.pages.length > 2 ? 'high' : 'medium'
    });
  });
}

/**
 * Generate detailed report
 */
function generateReport(analysis) {
  const report = {
    timestamp: new Date().toISOString(),
    atlas_version: '2.4',
    overall_passed: analysis.overall,
    summary: analysis.summary,
    pages: analysis.pages,
    recommendations: analysis.recommendations,
    next_steps: []
  };
  
  // Add next steps based on results
  if (!analysis.overall) {
    report.next_steps.push('Address critical performance and accessibility issues');
    report.next_steps.push('Implement recommended optimizations');
    report.next_steps.push('Re-run Lighthouse audit to verify improvements');
  } else {
    report.next_steps.push('Monitor performance metrics in production');
    report.next_steps.push('Set up continuous Lighthouse monitoring');
    report.next_steps.push('Consider advanced optimizations for edge cases');
  }
  
  // Save report
  const reportPath = 'reports/lighthouse-audit-report.json';
  fs.mkdirSync(path.dirname(reportPath), { recursive: true });
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  return report;
}

/**
 * Display results in console
 */
function displayResults(analysis) {
  log('\n' + '='.repeat(60), 'cyan');
  log('🚀 LIGHTHOUSE AUDIT RESULTS - ATLAS v2.4', 'cyan');
  log('='.repeat(60), 'cyan');
  
  // Overall status
  if (analysis.overall) {
    log('\n✅ ALL PAGES MEET LIGHTHOUSE TARGETS!', 'green');
  } else {
    log('\n❌ Some pages need optimization', 'red');
  }
  
  // Summary scores
  log('\n📊 Average Scores:', 'bold');
  Object.entries(analysis.summary.averageScores).forEach(([category, score]) => {
    const target = TARGET_SCORES[category];
    const color = score >= target ? 'green' : score >= target - 10 ? 'yellow' : 'red';
    const status = score >= target ? '✅' : '❌';
    
    log(`  ${status} ${category.padEnd(15)}: ${score}% (target: ${target}%)`, color);
  });
  
  // Page details
  log('\n📄 Page Results:', 'bold');
  analysis.pages.forEach(page => {
    const status = page.passed ? '✅' : '❌';
    const color = page.passed ? 'green' : 'red';
    
    log(`\n  ${status} ${page.name}`, color);
    Object.entries(page.scores).forEach(([category, data]) => {
      const scoreColor = data.passed ? 'green' : 'red';
      log(`    ${category}: ${data.score}%`, scoreColor);
    });
    
    if (page.opportunities.length > 0) {
      log(`    💡 ${page.opportunities.length} optimization opportunities`, 'yellow');
    }
  });
  
  // Top recommendations
  if (analysis.recommendations.length > 0) {
    log('\n💡 Top Recommendations:', 'yellow');
    analysis.recommendations.slice(0, 5).forEach((rec, index) => {
      log(`  ${index + 1}. ${rec.title}`, 'yellow');
      log(`     ${rec.recommendation}`, 'dim');
      log(`     Impact: ${rec.impact}, Savings: ${rec.savings}ms`, 'dim');
    });
  }
  
  log(`\n📄 Detailed report saved to: reports/lighthouse-audit-report.json`, 'blue');
}

/**
 * Main audit function
 */
async function runLighthouseOptimization() {
  log('🚀 Starting Lighthouse Optimization Audit...', 'cyan');
  log('============================================', 'cyan');
  
  try {
    // Check if server is running
    log('\n🔍 Checking if development server is running...', 'blue');
    
    const results = [];
    
    // Run audits for each URL
    for (const { url, name } of URLS_TO_AUDIT) {
      try {
        const result = await runLighthouseAudit(url, name);
        results.push(result);
        
        // Show quick score
        const perfScore = Math.round((result.scores.performance?.score || 0) * 100);
        const a11yScore = Math.round((result.scores.accessibility?.score || 0) * 100);
        log(`  Performance: ${perfScore}%, Accessibility: ${a11yScore}%`, 
            perfScore >= 95 && a11yScore >= 100 ? 'green' : 'yellow');
      } catch (error) {
        log(`  ❌ Failed to audit ${name}: ${error.message}`, 'red');
      }
    }
    
    if (results.length === 0) {
      log('❌ No successful audits. Make sure the development server is running.', 'red');
      return false;
    }
    
    // Analyze results
    const analysis = analyzeResults(results);
    
    // Generate and save report
    const report = generateReport(analysis);
    
    // Display results
    displayResults(analysis);
    
    return analysis.overall;
  } catch (error) {
    log(`❌ Lighthouse audit failed: ${error.message}`, 'red');
    return false;
  }
}

// Run if called directly
if (require.main === module) {
  runLighthouseOptimization()
    .then(success => {
      if (success) {
        log('\n🎉 Lighthouse optimization completed successfully!', 'green');
        process.exit(0);
      } else {
        log('\n🔧 Optimization needed. Check recommendations above.', 'yellow');
        process.exit(1);
      }
    })
    .catch(error => {
      log(`💥 Lighthouse optimization failed: ${error.message}`, 'red');
      process.exit(1);
    });
}

module.exports = { runLighthouseOptimization };
